<?php
/**
 * JobSpace - String Helper Functions
 * Professional String Manipulation
 * 
 * @package JobSpace\Helpers
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Escape HTML
 */
function e($value)
{
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

/**
 * Truncate string
 */
function str_limit($value, $limit = 100, $end = '...')
{
    if (mb_strwidth($value, 'UTF-8') <= $limit) {
        return $value;
    }

    return rtrim(mb_strimwidth($value, 0, $limit, '', 'UTF-8')) . $end;
}

/**
 * Generate random string
 */
function str_random($length = 16)
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Convert string to slug
 */
function str_slug($title, $separator = '-')
{
    $title = strtolower($title);
    $title = preg_replace('/[^a-z0-9\s-]/', '', $title);
    $title = preg_replace('/[\s-]+/', $separator, $title);
    return trim($title, $separator);
}

/**
 * Check if string starts with
 */
function str_starts_with($haystack, $needle)
{
    return substr($haystack, 0, strlen($needle)) === $needle;
}

/**
 * Check if string ends with
 */
function str_ends_with($haystack, $needle)
{
    return substr($haystack, -strlen($needle)) === $needle;
}

/**
 * Check if string contains
 */
function str_contains($haystack, $needle)
{
    return strpos($haystack, $needle) !== false;
}

/**
 * Convert to title case
 */
function str_title($value)
{
    return mb_convert_case($value, MB_CASE_TITLE, 'UTF-8');
}

/**
 * Convert to camel case
 */
function str_camel($value)
{
    return lcfirst(str_studly($value));
}

/**
 * Convert to studly case
 */
function str_studly($value)
{
    $value = ucwords(str_replace(['-', '_'], ' ', $value));
    return str_replace(' ', '', $value);
}

/**
 * Convert to snake case
 */
function str_snake($value, $delimiter = '_')
{
    if (!ctype_lower($value)) {
        $value = preg_replace('/\s+/u', '', ucwords($value));
        $value = strtolower(preg_replace('/(.)(?=[A-Z])/u', '$1' . $delimiter, $value));
    }
    
    return $value;
}

/**
 * Format bytes
 */
function format_bytes($size, $precision = 2)
{
    $base = log($size, 1024);
    $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

/**
 * Format number
 */
function format_number($number, $decimals = 0)
{
    return number_format($number, $decimals);
}

/**
 * Format currency
 */
function format_currency($amount, $currency = 'BDT')
{
    return $currency . ' ' . number_format($amount, 2);
}

/**
 * Clean string
 */
function clean_string($string)
{
    return trim(preg_replace('/\s+/', ' ', $string));
}

/**
 * Remove special characters
 */
function remove_special_chars($string)
{
    return preg_replace('/[^A-Za-z0-9\-]/', '', $string);
}

/**
 * Extract numbers from string
 */
function extract_numbers($string)
{
    preg_match_all('/\d+/', $string, $matches);
    return $matches[0];
}

/**
 * Mask string
 */
function mask_string($string, $start = 0, $length = null, $mask = '*')
{
    if ($length === null) {
        $length = strlen($string) - $start;
    }
    
    return substr_replace($string, str_repeat($mask, $length), $start, $length);
}

/**
 * Mask email
 */
function mask_email($email)
{
    $parts = explode('@', $email);
    if (count($parts) !== 2) {
        return $email;
    }
    
    $username = $parts[0];
    $domain = $parts[1];
    
    if (strlen($username) <= 2) {
        return $email;
    }
    
    $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
    
    return $maskedUsername . '@' . $domain;
}

/**
 * Mask phone number
 */
function mask_phone($phone)
{
    $length = strlen($phone);
    if ($length < 4) {
        return $phone;
    }
    
    return substr($phone, 0, 2) . str_repeat('*', $length - 4) . substr($phone, -2);
}

/**
 * Generate excerpt
 */
function excerpt($text, $length = 150, $ending = '...')
{
    $text = strip_tags($text);
    $text = clean_string($text);
    
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $text = substr($text, 0, $length);
    $lastSpace = strrpos($text, ' ');
    
    if ($lastSpace !== false) {
        $text = substr($text, 0, $lastSpace);
    }
    
    return $text . $ending;
}

/**
 * Highlight search terms
 */
function highlight_search($text, $search, $class = 'highlight')
{
    if (empty($search)) {
        return $text;
    }
    
    $search = preg_quote($search, '/');
    return preg_replace("/($search)/i", '<span class="' . $class . '">$1</span>', $text);
}

/**
 * Word count
 */
function word_count($text)
{
    return str_word_count(strip_tags($text));
}

/**
 * Reading time estimate
 */
function reading_time($text, $wpm = 200)
{
    $wordCount = word_count($text);
    $minutes = ceil($wordCount / $wpm);
    
    return $minutes . ' min read';
}

/**
 * Generate initials
 */
function get_initials($name, $length = 2)
{
    $words = explode(' ', trim($name));
    $initials = '';
    
    foreach ($words as $word) {
        if (strlen($initials) < $length && !empty($word)) {
            $initials .= strtoupper($word[0]);
        }
    }
    
    return $initials;
}

/**
 * Pluralize word
 */
function pluralize($word, $count)
{
    if ($count == 1) {
        return $word;
    }
    
    // Simple pluralization rules
    if (str_ends_with($word, 'y')) {
        return substr($word, 0, -1) . 'ies';
    } elseif (str_ends_with($word, 's') || str_ends_with($word, 'sh') || str_ends_with($word, 'ch')) {
        return $word . 'es';
    } else {
        return $word . 's';
    }
}

/**
 * Time ago format
 */
function time_ago($datetime)
{
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'just now';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' ' . pluralize('minute', $minutes) . ' ago';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' ' . pluralize('hour', $hours) . ' ago';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' ' . pluralize('day', $days) . ' ago';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return $months . ' ' . pluralize('month', $months) . ' ago';
    } else {
        $years = floor($time / 31536000);
        return $years . ' ' . pluralize('year', $years) . ' ago';
    }
}
