<?php
/**
 * JobSpace - Application Constants
 * Professional Configuration File
 *
 * @package JobSpace
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

// =============================================================================
// CORE PATHS
// =============================================================================

// Root directory
define('ROOT_PATH', dirname(__DIR__));

// Application paths
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('BOOTSTRAP_PATH', ROOT_PATH . '/bootstrap');
define('ROUTES_PATH', ROOT_PATH . '/routes');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('DATABASE_PATH', ROOT_PATH . '/database');

// App sub-paths
define('CONTROLLERS_PATH', APP_PATH . '/controllers');
define('MODELS_PATH', APP_PATH . '/models');
define('CORE_PATH', APP_PATH . '/core');
define('HELPERS_PATH', APP_PATH . '/helpers');
define('MIDDLEWARE_PATH', APP_PATH . '/middleware');

// View paths
define('TEMPLATES_PATH', VIEWS_PATH);
define('PAGES_PATH', VIEWS_PATH);
define('LAYOUTS_PATH', VIEWS_PATH . '/layouts');
define('INCLUDES_PATH', VIEWS_PATH . '/includes');
define('COMPONENTS_PATH', VIEWS_PATH . '/components');

// Public paths
define('ASSETS_PATH', PUBLIC_PATH . '/assets');
define('CSS_PATH', ASSETS_PATH . '/css');
define('JS_PATH', ASSETS_PATH . '/js');
define('IMG_PATH', ASSETS_PATH . '/images');

// =============================================================================
// URL CONFIGURATION
// =============================================================================

// Auto-detect base URL for both localhost and live server
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';

// Detect base path from script name
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$basePath = '';

// Extract base path properly
if (strpos($scriptName, '/public/index.php') !== false) {
    // Case: /jobspace/public/index.php -> /jobspace
    $basePath = str_replace('/public/index.php', '', $scriptName);
} elseif (strpos($scriptName, '/index.php') !== false) {
    // Case: /jobspace/index.php -> /jobspace
    $basePath = str_replace('/index.php', '', $scriptName);
} else {
    // Fallback: get directory from script name
    $basePath = dirname($scriptName);
    if ($basePath === '/' || $basePath === '\\') {
        $basePath = '';
    }
}

// Clean up base path
$basePath = rtrim($basePath, '/');

// Define URLs
define('BASE_URL', $protocol . $host . $basePath);
define('SITE_URL', BASE_URL);
define('ASSETS_URL', BASE_URL . '/public/assets');
define('UPLOADS_URL', BASE_URL . '/storage/uploads');

// =============================================================================
// APPLICATION SETTINGS
// =============================================================================

define('APP_NAME', 'JobSpace');
define('APP_VERSION', '1.0.0');
define('APP_DESCRIPTION', 'Complete Multi-Platform Ecosystem - Learn•Earn•Connect•Trade');
define('APP_KEYWORDS', 'jobs, freelance, quiz, education, social media, ecommerce, marketplace');
define('APP_AUTHOR', 'JobSpace Team');
define('APP_ENV', 'development'); // development, production, testing

// =============================================================================
// DATABASE SETTINGS
// =============================================================================

define('DB_HOST', 'localhost');
define('DB_NAME', 'jobspace');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// =============================================================================
// SECURITY SETTINGS
// =============================================================================

define('ENCRYPTION_KEY', 'your-secret-key-here');
define('SESSION_NAME', 'JOBSPACE_SESSION');
define('CSRF_TOKEN_NAME', '_token');

// =============================================================================
// CACHE & PERFORMANCE
// =============================================================================

define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('DEBUG_MODE', APP_ENV === 'development');

// =============================================================================
// TIMEZONE & LOCALE
// =============================================================================

define('DEFAULT_TIMEZONE', 'Asia/Dhaka');
define('DEFAULT_LOCALE', 'en_US');
define('DEFAULT_CURRENCY', 'BDT');

// Set timezone
date_default_timezone_set(DEFAULT_TIMEZONE);