<?php
/**
 * JobSpace - Application Constants
 * Professional Configuration File
 *
 * @package JobSpace
 * @version 1.0.0
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

// =============================================================================
// CORE PATHS
// =============================================================================

// Root directory
define('ROOT_PATH', dirname(__DIR__));

// Application paths
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('BOOTSTRAP_PATH', ROOT_PATH . '/bootstrap');
define('ROUTES_PATH', ROOT_PATH . '/routes');
define('RESOURCES_PATH', ROOT_PATH . '/resources');
define('DATABASE_PATH', ROOT_PATH . '/database');
define('TESTS_PATH', ROOT_PATH . '/tests');
define('DOCS_PATH', ROOT_PATH . '/docs');

// App sub-paths
define('CONTROLLERS_PATH', APP_PATH . '/controllers');
define('MODELS_PATH', APP_PATH . '/models');
define('CORE_PATH', APP_PATH . '/core');
define('HELPERS_PATH', APP_PATH . '/helpers');
define('MIDDLEWARE_PATH', APP_PATH . '/middleware');
define('SERVICES_PATH', APP_PATH . '/services');
define('MODULES_PATH', APP_PATH . '/modules');

// View paths
define('VIEWS_PATH', ROOT_PATH . '/views');
define('TEMPLATES_PATH', VIEWS_PATH);
define('PAGES_PATH', VIEWS_PATH);
define('LAYOUTS_PATH', VIEWS_PATH . '/layouts');
define('INCLUDES_PATH', VIEWS_PATH . '/includes');
define('COMPONENTS_PATH', VIEWS_PATH . '/components');

// Storage paths
define('CACHE_PATH', STORAGE_PATH . '/cache');
define('LOGS_PATH', STORAGE_PATH . '/logs');
define('UPLOADS_PATH', STORAGE_PATH . '/uploads');
define('BACKUPS_PATH', DATABASE_PATH . '/backups');

// Public asset paths
define('ASSETS_PATH', PUBLIC_PATH . '/assets');
define('CSS_PATH', ASSETS_PATH . '/css');
define('JS_PATH', ASSETS_PATH . '/js');
define('IMG_PATH', ASSETS_PATH . '/images');

// =============================================================================
// URL CONFIGURATION
// =============================================================================

// Auto-detect base URL for both localhost and live server
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$basePath = str_replace('/public/index.php', '', $scriptName);
$basePath = str_replace('/index.php', '', $basePath);

define('BASE_URL', $protocol . $host . $basePath);
define('SITE_URL', BASE_URL);
define('ASSETS_URL', BASE_URL . '/public/assets');
define('UPLOADS_URL', BASE_URL . '/storage/uploads');

// =============================================================================
// APPLICATION SETTINGS
// =============================================================================

define('APP_NAME', 'JobSpace');
define('APP_VERSION', '1.0.0');
define('APP_DESCRIPTION', 'Complete Multi-Platform Ecosystem - Learn•Earn•Connect•Trade');
define('APP_KEYWORDS', 'jobs, freelance, quiz, education, social media, ecommerce, marketplace');
define('APP_AUTHOR', 'JobSpace Team');
define('APP_ENV', 'development'); // development, production, testing

// =============================================================================
// SECURITY SETTINGS
// =============================================================================

define('SECURITY_KEY', 'jobspace_secure_key_2024');
define('CSRF_TOKEN_NAME', '_token');
define('SESSION_NAME', 'jobspace_session');
define('COOKIE_PREFIX', 'jobspace_');

// =============================================================================
// PAGINATION & LIMITS
// =============================================================================

define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);
define('SEARCH_RESULTS_LIMIT', 50);

// =============================================================================
// FILE UPLOAD SETTINGS
// =============================================================================

define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// =============================================================================
// CACHE SETTINGS
// =============================================================================

define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('VIEW_CACHE_ENABLED', false); // Disable in development

// =============================================================================
// TIMEZONE & LOCALE
// =============================================================================

define('DEFAULT_TIMEZONE', 'Asia/Dhaka');
define('DEFAULT_LOCALE', 'en');
define('DATE_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd M Y');

// =============================================================================
// ERROR HANDLING
// =============================================================================

define('DEBUG_MODE', APP_ENV === 'development');
define('LOG_ERRORS', true);
define('DISPLAY_ERRORS', DEBUG_MODE);

// =============================================================================
// ROLES & PERMISSIONS
// =============================================================================

define('ROLE_ADMIN', 'admin');
define('ROLE_CONTRIBUTOR', 'contributor');
define('ROLE_USER', 'user');

define('DEFAULT_ROLE', ROLE_USER);

// =============================================================================
// MODULES
// =============================================================================

define('MODULES', [
    'quiz' => 'Quiz & Education',
    'freelance' => 'Freelance Marketplace',
    'social' => 'Social Media',
    'ecommerce' => 'E-commerce',
    'payment' => 'Payment System',
    'analytics' => 'Analytics'
]);

// =============================================================================
// API SETTINGS
// =============================================================================

define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // requests per hour
define('API_TOKEN_EXPIRY', 86400); // 24 hours

// Set timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Get application URL
 */
function app_url($path = '') {
    return BASE_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Get asset URL
 */
function asset_url($path = '') {
    return ASSETS_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Get upload URL
 */
function upload_url($path = '') {
    return UPLOADS_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Check if in development mode
 */
function is_development() {
    return APP_ENV === 'development';
}

/**
 * Check if in production mode
 */
function is_production() {
    return APP_ENV === 'production';
}