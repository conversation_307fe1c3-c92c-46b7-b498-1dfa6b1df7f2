<?php
/**
 * JobSpace - Home Controller
 * Professional Home Page Controller
 * 
 * @package JobSpace\Controllers
 * @version 1.0.0
 */

class HomeController extends Controller
{
    /**
     * Initialize controller
     */
    protected function init()
    {
        // Set common data for all home methods
        $this->setData([
            'pageTitle' => (defined('APP_NAME') ? APP_NAME : 'JobSpace') . ' - ' . (defined('APP_DESCRIPTION') ? APP_DESCRIPTION : 'Complete Multi-Platform Ecosystem'),
            'pageDescription' => defined('APP_DESCRIPTION') ? APP_DESCRIPTION : 'Complete Multi-Platform Ecosystem - Learn•Earn•Connect•Trade',
            'pageKeywords' => defined('APP_KEYWORDS') ? APP_KEYWORDS : 'jobs, freelance, quiz, education, social media, ecommerce, marketplace',
        ]);
    }
    
    /**
     * Display home page
     */
    public function index()
    {
        try {
            // Get dynamic data for home page
            $data = $this->getHomePageData();
            
            // Set page-specific data
            $this->setData($data);
            
            // Render home page
            return $this->view('pages/home');
            
        } catch (Exception $e) {
            // Log error
            error_log("Home page error: " . $e->getMessage());
            
            // Return error view or fallback
            if (DEBUG_MODE) {
                throw $e;
            } else {
                return $this->view('errors/500', [
                    'message' => 'Unable to load home page. Please try again later.'
                ]);
            }
        }
    }
    
    /**
     * Get dynamic data for home page
     */
    private function getHomePageData()
    {
        $data = [];
        
        try {
            // Get statistics
            $data['stats'] = $this->getStatistics();
            
            // Get featured content
            $data['featuredQuizzes'] = $this->getFeaturedQuizzes();
            $data['latestJobs'] = $this->getLatestJobs();
            $data['featuredProducts'] = $this->getFeaturedProducts();
            
            // Get testimonials
            $data['testimonials'] = $this->getTestimonials();
            
            // Get latest news/updates
            $data['latestNews'] = $this->getLatestNews();
            
            // Get platform features
            $data['features'] = $this->getPlatformFeatures();
            
            // Get how it works steps
            $data['howItWorks'] = $this->getHowItWorksSteps();
            
        } catch (Exception $e) {
            // Log error but don't break the page
            error_log("Error loading home page data: " . $e->getMessage());
            
            // Set fallback data
            $data = $this->getFallbackData();
        }
        
        return $data;
    }
    
    /**
     * Get platform statistics
     */
    private function getStatistics()
    {
        try {
            $stats = [
                'totalUsers' => 0,
                'totalQuizzes' => 0,
                'totalJobs' => 0,
                'totalProducts' => 0,
                'totalEarnings' => 0,
            ];

            // Check if database is available
            if (!$this->db) {
                return $this->getFallbackStats();
            }

            // Get user count
            $userCount = $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
            $stats['totalUsers'] = $userCount ? $userCount['count'] : 0;
            
            // Get quiz count
            $quizCount = $this->db->fetch("SELECT COUNT(*) as count FROM quizzes WHERE status = 'published'");
            $stats['totalQuizzes'] = $quizCount ? $quizCount['count'] : 0;
            
            // Get job count
            $jobCount = $this->db->fetch("SELECT COUNT(*) as count FROM jobs WHERE status = 'active'");
            $stats['totalJobs'] = $jobCount ? $jobCount['count'] : 0;
            
            // Get product count
            $productCount = $this->db->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
            $stats['totalProducts'] = $productCount ? $productCount['count'] : 0;
            
            // Format numbers for display
            $stats['totalUsersFormatted'] = $this->formatNumber($stats['totalUsers']);
            $stats['totalQuizzesFormatted'] = $this->formatNumber($stats['totalQuizzes']);
            $stats['totalJobsFormatted'] = $this->formatNumber($stats['totalJobs']);
            $stats['totalProductsFormatted'] = $this->formatNumber($stats['totalProducts']);
            
            return $stats;
            
        } catch (Exception $e) {
            // Return default stats if database error
            return $this->getFallbackStats();
        }
    }

    /**
     * Get fallback statistics
     */
    private function getFallbackStats()
    {
        return [
            'totalUsers' => 1000,
            'totalQuizzes' => 500,
            'totalJobs' => 200,
            'totalProducts' => 300,
            'totalUsersFormatted' => '1K+',
            'totalQuizzesFormatted' => '500+',
            'totalJobsFormatted' => '200+',
            'totalProductsFormatted' => '300+',
        ];
    }
    
    /**
     * Get featured quizzes
     */
    private function getFeaturedQuizzes()
    {
        try {
            if (!$this->db) {
                return $this->getFallbackQuizzes();
            }

            $quizzes = $this->db->fetchAll("
                SELECT id, title, description, category, difficulty,
                       questions_count, participants_count, created_at
                FROM quizzes
                WHERE status = 'published' AND featured = 1
                ORDER BY created_at DESC
                LIMIT 6
            ");

            return $quizzes ?: [];

        } catch (Exception $e) {
            return $this->getFallbackQuizzes();
        }
    }
    
    /**
     * Get latest jobs
     */
    private function getLatestJobs()
    {
        try {
            $jobs = $this->db->fetchAll("
                SELECT id, title, description, company, location, 
                       salary_min, salary_max, job_type, created_at
                FROM jobs 
                WHERE status = 'active'
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $jobs ?: [];
            
        } catch (Exception $e) {
            return $this->getFallbackJobs();
        }
    }
    
    /**
     * Get featured products
     */
    private function getFeaturedProducts()
    {
        try {
            $products = $this->db->fetchAll("
                SELECT id, name, description, price, image, 
                       category, rating, created_at
                FROM products 
                WHERE status = 'active' AND featured = 1
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $products ?: [];
            
        } catch (Exception $e) {
            return $this->getFallbackProducts();
        }
    }
    
    /**
     * Get testimonials
     */
    private function getTestimonials()
    {
        try {
            $testimonials = $this->db->fetchAll("
                SELECT name, message, rating, avatar, position, company
                FROM testimonials 
                WHERE status = 'approved'
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $testimonials ?: $this->getFallbackTestimonials();
            
        } catch (Exception $e) {
            return $this->getFallbackTestimonials();
        }
    }
    
    /**
     * Get latest news
     */
    private function getLatestNews()
    {
        try {
            $news = $this->db->fetchAll("
                SELECT id, title, excerpt, image, created_at
                FROM news 
                WHERE status = 'published'
                ORDER BY created_at DESC 
                LIMIT 3
            ");
            
            return $news ?: [];
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get platform features
     */
    private function getPlatformFeatures()
    {
        return [
            [
                'icon' => 'fas fa-brain',
                'title' => 'Quiz & Education',
                'description' => 'Test your knowledge with interactive quizzes and earn rewards for learning.',
                'color' => 'blue'
            ],
            [
                'icon' => 'fas fa-briefcase',
                'title' => 'Freelance Marketplace',
                'description' => 'Find freelance opportunities or hire talented professionals for your projects.',
                'color' => 'green'
            ],
            [
                'icon' => 'fas fa-users',
                'title' => 'Social Network',
                'description' => 'Connect with like-minded people and build your professional network.',
                'color' => 'purple'
            ],
            [
                'icon' => 'fas fa-shopping-cart',
                'title' => 'E-commerce',
                'description' => 'Buy and sell products in our integrated marketplace with secure payments.',
                'color' => 'orange'
            ],
            [
                'icon' => 'fas fa-wallet',
                'title' => 'Digital Wallet',
                'description' => 'Manage your earnings and payments with our secure digital wallet system.',
                'color' => 'indigo'
            ],
            [
                'icon' => 'fas fa-chart-line',
                'title' => 'Analytics',
                'description' => 'Track your progress and performance with detailed analytics and insights.',
                'color' => 'red'
            ]
        ];
    }
    
    /**
     * Get how it works steps
     */
    private function getHowItWorksSteps()
    {
        return [
            [
                'step' => 1,
                'title' => 'Sign Up',
                'description' => 'Create your free account and choose your interests.',
                'icon' => 'fas fa-user-plus'
            ],
            [
                'step' => 2,
                'title' => 'Explore',
                'description' => 'Discover quizzes, jobs, products, and connect with others.',
                'icon' => 'fas fa-compass'
            ],
            [
                'step' => 3,
                'title' => 'Participate',
                'description' => 'Take quizzes, apply for jobs, buy/sell products, and engage socially.',
                'icon' => 'fas fa-play'
            ],
            [
                'step' => 4,
                'title' => 'Earn',
                'description' => 'Earn money through various activities and grow your income.',
                'icon' => 'fas fa-coins'
            ]
        ];
    }
    
    /**
     * Format number for display
     */
    private function formatNumber($number)
    {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M+';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K+';
        } else {
            return $number . '+';
        }
    }
    
    /**
     * Get fallback data when database is not available
     */
    private function getFallbackData()
    {
        return [
            'stats' => [
                'totalUsers' => 1000,
                'totalQuizzes' => 500,
                'totalJobs' => 200,
                'totalProducts' => 300,
                'totalUsersFormatted' => '1K+',
                'totalQuizzesFormatted' => '500+',
                'totalJobsFormatted' => '200+',
                'totalProductsFormatted' => '300+',
            ],
            'featuredQuizzes' => $this->getFallbackQuizzes(),
            'latestJobs' => $this->getFallbackJobs(),
            'featuredProducts' => $this->getFallbackProducts(),
            'testimonials' => $this->getFallbackTestimonials(),
            'latestNews' => [],
            'features' => $this->getPlatformFeatures(),
            'howItWorks' => $this->getHowItWorksSteps(),
        ];
    }
    
    /**
     * Get fallback quizzes
     */
    private function getFallbackQuizzes()
    {
        return [
            [
                'id' => 1,
                'title' => 'Web Development Basics',
                'description' => 'Test your knowledge of HTML, CSS, and JavaScript fundamentals.',
                'category' => 'Programming',
                'difficulty' => 'Beginner',
                'questions_count' => 20,
                'participants_count' => 150
            ],
            [
                'id' => 2,
                'title' => 'Digital Marketing Quiz',
                'description' => 'Assess your understanding of modern digital marketing strategies.',
                'category' => 'Marketing',
                'difficulty' => 'Intermediate',
                'questions_count' => 25,
                'participants_count' => 89
            ],
            [
                'id' => 3,
                'title' => 'General Knowledge',
                'description' => 'Challenge yourself with questions from various topics.',
                'category' => 'General',
                'difficulty' => 'Mixed',
                'questions_count' => 30,
                'participants_count' => 245
            ]
        ];
    }
    
    /**
     * Get fallback jobs
     */
    private function getFallbackJobs()
    {
        return [
            [
                'id' => 1,
                'title' => 'Frontend Developer',
                'description' => 'Looking for a skilled React.js developer for our startup.',
                'company' => 'TechStart Inc.',
                'location' => 'Remote',
                'salary_min' => 50000,
                'salary_max' => 80000,
                'job_type' => 'Full-time'
            ],
            [
                'id' => 2,
                'title' => 'Content Writer',
                'description' => 'Need an experienced content writer for blog posts and articles.',
                'company' => 'Content Agency',
                'location' => 'Dhaka, Bangladesh',
                'salary_min' => 25000,
                'salary_max' => 40000,
                'job_type' => 'Part-time'
            ],
            [
                'id' => 3,
                'title' => 'Graphic Designer',
                'description' => 'Creative graphic designer needed for brand identity projects.',
                'company' => 'Design Studio',
                'location' => 'Hybrid',
                'salary_min' => 35000,
                'salary_max' => 60000,
                'job_type' => 'Contract'
            ]
        ];
    }
    
    /**
     * Get fallback products
     */
    private function getFallbackProducts()
    {
        return [
            [
                'id' => 1,
                'name' => 'Web Development Course',
                'description' => 'Complete web development course from beginner to advanced.',
                'price' => 99.99,
                'image' => '/assets/images/products/web-course.jpg',
                'category' => 'Education',
                'rating' => 4.8
            ],
            [
                'id' => 2,
                'name' => 'Premium Template Pack',
                'description' => 'Collection of 50+ premium website templates.',
                'price' => 49.99,
                'image' => '/assets/images/products/templates.jpg',
                'category' => 'Digital',
                'rating' => 4.6
            ],
            [
                'id' => 3,
                'name' => 'SEO Tools Bundle',
                'description' => 'Professional SEO tools for website optimization.',
                'price' => 199.99,
                'image' => '/assets/images/products/seo-tools.jpg',
                'category' => 'Software',
                'rating' => 4.9
            ]
        ];
    }
    
    /**
     * Get fallback testimonials
     */
    private function getFallbackTestimonials()
    {
        return [
            [
                'name' => 'Sarah Johnson',
                'message' => 'JobSpace has transformed my career! I found amazing freelance opportunities and improved my skills through quizzes.',
                'rating' => 5,
                'avatar' => '/assets/images/avatars/sarah.jpg',
                'position' => 'Freelance Developer',
                'company' => 'Independent'
            ],
            [
                'name' => 'Ahmed Rahman',
                'message' => 'The quiz system is fantastic for learning. I earned money while improving my knowledge. Highly recommended!',
                'rating' => 5,
                'avatar' => '/assets/images/avatars/ahmed.jpg',
                'position' => 'Student',
                'company' => 'University of Dhaka'
            ],
            [
                'name' => 'Emily Chen',
                'message' => 'Great platform for networking and finding quality products. The social features are well-designed.',
                'rating' => 4,
                'avatar' => '/assets/images/avatars/emily.jpg',
                'position' => 'Marketing Manager',
                'company' => 'Digital Agency'
            ]
        ];
    }
}
