<?php
/**
 * JobSpace - Home Controller
 * Professional Home Page Controller
 *
 * @package JobSpace\Controllers
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class HomeController extends Controller
{
    /**
     * Initialize controller
     */
    protected function init()
    {
        // Set common data for all home methods
        $this->setData([
            'pageTitle' => APP_NAME . ' - ' . APP_DESCRIPTION,
            'pageDescription' => APP_DESCRIPTION,
            'pageKeywords' => APP_KEYWORDS,
        ]);
    }

    /**
     * Home page
     */
    public function index(Request $request)
    {
        // Get platform statistics
        $stats = $this->getStatistics();
        
        // Get featured content
        $featuredQuizzes = $this->getFeaturedQuizzes();
        $featuredJobs = $this->getFeaturedJobs();
        $featuredProducts = $this->getFeaturedProducts();
        
        // Set page data
        $this->setData([
            'pageTitle' => APP_NAME . ' - Complete Multi-Platform Ecosystem',
            'stats' => $stats,
            'featuredQuizzes' => $featuredQuizzes,
            'featuredJobs' => $featuredJobs,
            'featuredProducts' => $featuredProducts,
        ]);

        return $this->view('pages/home');
    }

    /**
     * About page
     */
    public function about(Request $request)
    {
        $this->setData([
            'pageTitle' => 'About Us - ' . APP_NAME,
            'pageDescription' => 'Learn more about JobSpace and our mission to create a complete multi-platform ecosystem.',
        ]);

        return $this->view('pages/about');
    }

    /**
     * Contact page
     */
    public function contact(Request $request)
    {
        $this->setData([
            'pageTitle' => 'Contact Us - ' . APP_NAME,
            'pageDescription' => 'Get in touch with the JobSpace team.',
        ]);

        if ($request->isPost()) {
            return $this->handleContactForm($request);
        }

        return $this->view('pages/contact');
    }

    /**
     * Privacy policy page
     */
    public function privacy(Request $request)
    {
        $this->setData([
            'pageTitle' => 'Privacy Policy - ' . APP_NAME,
            'pageDescription' => 'Read our privacy policy and learn how we protect your data.',
        ]);

        return $this->view('pages/privacy');
    }

    /**
     * Terms of service page
     */
    public function terms(Request $request)
    {
        $this->setData([
            'pageTitle' => 'Terms of Service - ' . APP_NAME,
            'pageDescription' => 'Read our terms of service and user agreement.',
        ]);

        return $this->view('pages/terms');
    }

    /**
     * Help center page
     */
    public function help(Request $request)
    {
        $this->setData([
            'pageTitle' => 'Help Center - ' . APP_NAME,
            'pageDescription' => 'Find answers to frequently asked questions and get help.',
        ]);

        return $this->view('pages/help');
    }

    /**
     * FAQ page
     */
    public function faq(Request $request)
    {
        $this->setData([
            'pageTitle' => 'FAQ - ' . APP_NAME,
            'pageDescription' => 'Frequently asked questions about JobSpace.',
        ]);

        return $this->view('pages/faq');
    }

    /**
     * Get platform statistics
     */
    private function getStatistics()
    {
        try {
            $stats = [
                'totalUsers' => 0,
                'totalQuizzes' => 0,
                'totalJobs' => 0,
                'totalProducts' => 0,
                'totalEarnings' => 0,
            ];
            
            // Check if database is available
            if (!$this->db) {
                return $this->getFallbackStats();
            }
            
            // Get user count (with fallback)
            try {
                $userCount = $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
                $stats['totalUsers'] = $userCount ? $userCount['count'] : 0;
            } catch (Exception $e) {
                $stats['totalUsers'] = 1000; // Fallback
            }

            // Get quiz count (with fallback)
            try {
                $quizCount = $this->db->fetch("SELECT COUNT(*) as count FROM quizzes WHERE status = 'published'");
                $stats['totalQuizzes'] = $quizCount ? $quizCount['count'] : 0;
            } catch (Exception $e) {
                $stats['totalQuizzes'] = 500; // Fallback
            }

            // Get job count (with fallback)
            try {
                $jobCount = $this->db->fetch("SELECT COUNT(*) as count FROM jobs WHERE status = 'active'");
                $stats['totalJobs'] = $jobCount ? $jobCount['count'] : 0;
            } catch (Exception $e) {
                $stats['totalJobs'] = 200; // Fallback
            }

            // Get product count (with fallback)
            try {
                $productCount = $this->db->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
                $stats['totalProducts'] = $productCount ? $productCount['count'] : 0;
            } catch (Exception $e) {
                $stats['totalProducts'] = 300; // Fallback
            }

            // Format numbers
            $stats['totalUsersFormatted'] = $this->formatNumber($stats['totalUsers']);
            $stats['totalQuizzesFormatted'] = $this->formatNumber($stats['totalQuizzes']);
            $stats['totalJobsFormatted'] = $this->formatNumber($stats['totalJobs']);
            $stats['totalProductsFormatted'] = $this->formatNumber($stats['totalProducts']);

            return $stats;

        } catch (Exception $e) {
            // Return default stats if database error
            return $this->getFallbackStats();
        }
    }
    
    /**
     * Get fallback statistics
     */
    private function getFallbackStats()
    {
        return [
            'totalUsers' => 1000,
            'totalQuizzes' => 500,
            'totalJobs' => 200,
            'totalProducts' => 300,
            'totalUsersFormatted' => '1K+',
            'totalQuizzesFormatted' => '500+',
            'totalJobsFormatted' => '200+',
            'totalProductsFormatted' => '300+',
        ];
    }

    /**
     * Get featured quizzes
     */
    private function getFeaturedQuizzes()
    {
        try {
            if (!$this->db) {
                return $this->getFallbackQuizzes();
            }
            
            $quizzes = $this->db->fetchAll("
                SELECT id, title, description, category, difficulty, 
                       questions_count, participants_count, created_at
                FROM quizzes 
                WHERE status = 'published' AND featured = 1
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $quizzes ?: $this->getFallbackQuizzes();
            
        } catch (Exception $e) {
            return $this->getFallbackQuizzes();
        }
    }

    /**
     * Get fallback quizzes
     */
    private function getFallbackQuizzes()
    {
        return [
            [
                'id' => 1,
                'title' => 'Web Development Basics',
                'description' => 'Test your knowledge of HTML, CSS, and JavaScript fundamentals.',
                'category' => 'Programming',
                'difficulty' => 'Beginner',
                'questions_count' => 20,
                'participants_count' => 150,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'title' => 'Digital Marketing Essentials',
                'description' => 'Learn the basics of digital marketing and SEO.',
                'category' => 'Marketing',
                'difficulty' => 'Intermediate',
                'questions_count' => 15,
                'participants_count' => 89,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 3,
                'title' => 'Business Management',
                'description' => 'Essential business management principles and practices.',
                'category' => 'Business',
                'difficulty' => 'Advanced',
                'questions_count' => 25,
                'participants_count' => 67,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * Get featured jobs
     */
    private function getFeaturedJobs()
    {
        try {
            if (!$this->db) {
                return $this->getFallbackJobs();
            }
            
            $jobs = $this->db->fetchAll("
                SELECT id, title, description, company, location, 
                       salary_min, salary_max, job_type, created_at
                FROM jobs 
                WHERE status = 'active' AND featured = 1
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $jobs ?: $this->getFallbackJobs();
            
        } catch (Exception $e) {
            return $this->getFallbackJobs();
        }
    }

    /**
     * Get fallback jobs
     */
    private function getFallbackJobs()
    {
        return [
            [
                'id' => 1,
                'title' => 'Full Stack Developer',
                'description' => 'Looking for an experienced full stack developer to join our team.',
                'company' => 'TechCorp',
                'location' => 'Remote',
                'salary_min' => 50000,
                'salary_max' => 80000,
                'job_type' => 'Full-time',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'title' => 'UI/UX Designer',
                'description' => 'Creative UI/UX designer needed for mobile app design.',
                'company' => 'DesignStudio',
                'location' => 'New York',
                'salary_min' => 40000,
                'salary_max' => 60000,
                'job_type' => 'Contract',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * Get featured products
     */
    private function getFeaturedProducts()
    {
        try {
            if (!$this->db) {
                return $this->getFallbackProducts();
            }
            
            $products = $this->db->fetchAll("
                SELECT id, name, description, price, image, 
                       category, rating, created_at
                FROM products 
                WHERE status = 'active' AND featured = 1
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            
            return $products ?: $this->getFallbackProducts();
            
        } catch (Exception $e) {
            return $this->getFallbackProducts();
        }
    }

    /**
     * Get fallback products
     */
    private function getFallbackProducts()
    {
        return [
            [
                'id' => 1,
                'name' => 'Programming Course Bundle',
                'description' => 'Complete programming course with certificates.',
                'price' => 99.99,
                'image' => '/assets/images/products/course1.jpg',
                'category' => 'Education',
                'rating' => 4.8,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'name' => 'Business Templates Pack',
                'description' => 'Professional business document templates.',
                'price' => 29.99,
                'image' => '/assets/images/products/templates1.jpg',
                'category' => 'Business',
                'rating' => 4.5,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * Format number for display
     */
    private function formatNumber($number)
    {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M+';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K+';
        } else {
            return $number . '+';
        }
    }

    /**
     * Handle contact form submission
     */
    private function handleContactForm(Request $request)
    {
        // Get form data
        $name = $request->input('name');
        $email = $request->input('email');
        $subject = $request->input('subject');
        $message = $request->input('message');

        // Basic validation
        if (empty($name) || empty($email) || empty($message)) {
            $this->flash('error', 'Please fill in all required fields.');
            return $this->back();
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->flash('error', 'Please enter a valid email address.');
            return $this->back();
        }

        try {
            // Here you would typically save to database or send email
            // For now, just show success message
            $this->flash('success', 'Thank you for your message! We will get back to you soon.');
            return $this->redirect(BASE_URL . '/contact');
            
        } catch (Exception $e) {
            $this->flash('error', 'Sorry, there was an error sending your message. Please try again.');
            return $this->back();
        }
    }
}
