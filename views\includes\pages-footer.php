<!-- JobSpace - Public Footer Component -->
<footer class="bg-gray-900 text-white">
    <!-- Main Footer -->
    <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            
            <!-- Company Info -->
            <div class="space-y-4">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">J</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold"><?= $appName ?? 'JobSpace' ?></h3>
                        <p class="text-sm text-gray-400 -mt-1">Learn • Earn • Connect • Trade</p>
                    </div>
                </div>
                <p class="text-gray-300 text-sm leading-relaxed">
                    JobSpace is a comprehensive multi-platform ecosystem that combines education, freelancing, 
                    social networking, and e-commerce in one powerful platform.
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-facebook-f text-lg"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-twitter text-lg"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-linkedin-in text-lg"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-instagram text-lg"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-youtube text-lg"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="space-y-4">
                <h4 class="text-lg font-semibold text-white">Quick Links</h4>
                <ul class="space-y-2">
                    <li>
                        <a href="/" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-home mr-2"></i> Home
                        </a>
                    </li>
                    <li>
                        <a href="/about" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-info-circle mr-2"></i> About Us
                        </a>
                    </li>
                    <li>
                        <a href="/quiz" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-brain mr-2"></i> Quizzes
                        </a>
                    </li>
                    <li>
                        <a href="/freelance" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-briefcase mr-2"></i> Jobs
                        </a>
                    </li>
                    <li>
                        <a href="/shop" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-shopping-cart mr-2"></i> Shop
                        </a>
                    </li>
                    <li>
                        <a href="/contact" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-envelope mr-2"></i> Contact
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Features -->
            <div class="space-y-4">
                <h4 class="text-lg font-semibold text-white">Features</h4>
                <ul class="space-y-2">
                    <li>
                        <a href="/quiz" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-graduation-cap mr-2"></i> Interactive Learning
                        </a>
                    </li>
                    <li>
                        <a href="/freelance" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-handshake mr-2"></i> Freelance Marketplace
                        </a>
                    </li>
                    <li>
                        <a href="/social" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-users mr-2"></i> Social Networking
                        </a>
                    </li>
                    <li>
                        <a href="/shop" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-store mr-2"></i> E-commerce Platform
                        </a>
                    </li>
                    <li>
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-wallet mr-2"></i> Digital Wallet
                        </a>
                    </li>
                    <li>
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <i class="fas fa-chart-line mr-2"></i> Analytics Dashboard
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="space-y-4">
                <h4 class="text-lg font-semibold text-white">Contact Info</h4>
                <div class="space-y-3">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-map-marker-alt text-blue-400 mt-1"></i>
                        <div>
                            <p class="text-gray-300 text-sm">
                                123 Tech Street<br>
                                Dhaka, Bangladesh 1000
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-phone text-blue-400"></i>
                        <a href="tel:+8801234567890" class="text-gray-300 hover:text-white transition-colors text-sm">
                            +880 1234-567890
                        </a>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-envelope text-blue-400"></i>
                        <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors text-sm">
                            <EMAIL>
                        </a>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-clock text-blue-400"></i>
                        <p class="text-gray-300 text-sm">
                            24/7 Support Available
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Section -->
    <div class="bg-gray-800 border-t border-gray-700">
        <div class="container mx-auto px-4 py-8">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                <div class="text-center md:text-left">
                    <h4 class="text-lg font-semibold text-white mb-2">Stay Updated</h4>
                    <p class="text-gray-300 text-sm">Subscribe to our newsletter for latest updates and opportunities.</p>
                </div>
                <form action="/newsletter/subscribe" method="POST" class="flex w-full md:w-auto">
                    <?= View::csrf() ?>
                    <input type="email" name="email" placeholder="Enter your email" required
                           class="flex-1 md:w-64 px-4 py-2 bg-gray-700 text-white placeholder-gray-400 border border-gray-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="bg-gray-800 border-t border-gray-700">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                <div class="text-center md:text-left">
                    <p class="text-gray-400 text-sm">
                        © <?= date('Y') ?> <?= $appName ?? 'JobSpace' ?>. All rights reserved.
                        <?php if (isset($appVersion)): ?>
                            <span class="ml-2 text-xs">v<?= $appVersion ?></span>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="flex flex-wrap justify-center md:justify-end space-x-6">
                    <a href="/privacy" class="text-gray-400 hover:text-white transition-colors text-sm">
                        Privacy Policy
                    </a>
                    <a href="/terms" class="text-gray-400 hover:text-white transition-colors text-sm">
                        Terms of Service
                    </a>
                    <a href="/faq" class="text-gray-400 hover:text-white transition-colors text-sm">
                        FAQ
                    </a>
                    <a href="/help" class="text-gray-400 hover:text-white transition-colors text-sm">
                        Help Center
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button id="back-to-top" 
            class="fixed bottom-6 right-6 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible">
        <i class="fas fa-chevron-up"></i>
    </button>
</footer>

<!-- Footer Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Back to top button functionality
    const backToTopBtn = document.getElementById('back-to-top');
    
    if (backToTopBtn) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.add('opacity-0', 'invisible');
                backToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });
        
        // Smooth scroll to top
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // Newsletter form handling
    const newsletterForm = document.querySelector('form[action="/newsletter/subscribe"]');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[name="email"]').value;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitBtn.disabled = true;
            
            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 1000);
        });
    }
});
</script>
