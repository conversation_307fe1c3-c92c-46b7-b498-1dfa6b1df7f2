<?php
/**
 * JobSpace - View Core Class
 * Professional View Rendering System
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class View
{
    private static $data = [];
    private static $sections = [];
    private static $currentSection = null;
    private static $layouts = [];
    private static $config = [];

    /**
     * Initialize view system
     */
    public static function init()
    {
        self::$config = require CONFIG_PATH . '/app.php';
    }

    /**
     * Render a view
     */
    public static function render($view, $data = [], $layout = null)
    {
        // Merge data
        $data = array_merge(self::$data, $data);

        // Extract variables
        extract($data);

        // Start output buffering
        ob_start();

        try {
            // Find and include view file
            $viewFile = self::findViewFile($view);

            if (!$viewFile) {
                throw new Exception("View file not found: {$view}");
            }

            include $viewFile;

            $content = ob_get_clean();

            // If layout is specified, render with layout
            if ($layout) {
                return self::renderWithLayout($content, $layout, $data);
            }

            return $content;

        } catch (Exception $e) {
            ob_end_clean();

            if (DEBUG_MODE) {
                throw $e;
            } else {
                error_log("View rendering error: " . $e->getMessage());
                return self::renderError('View rendering failed');
            }
        }
    }

    /**
     * Render view with layout
     */
    private static function renderWithLayout($content, $layout, $data)
    {
        // Set content section
        self::$sections['content'] = $content;

        // Extract variables for layout
        extract($data);

        // Start output buffering for layout
        ob_start();

        try {
            $layoutFile = self::findLayoutFile($layout);

            if (!$layoutFile) {
                throw new Exception("Layout file not found: {$layout}");
            }

            include $layoutFile;

            return ob_get_clean();

        } catch (Exception $e) {
            ob_end_clean();
            throw $e;
        }
    }

    /**
     * Find view file
     */
    private static function findViewFile($view)
    {
        $paths = [
            VIEWS_PATH . '/' . $view . '.php',
            TEMPLATES_PATH . '/' . $view . '.php',
            COMPONENTS_PATH . '/' . $view . '.php',
        ];

        foreach ($paths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Find layout file
     */
    private static function findLayoutFile($layout)
    {
        $paths = [
            LAYOUTS_PATH . '/' . $layout . '.php',
            VIEWS_PATH . '/layouts/' . $layout . '.php',
        ];

        foreach ($paths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Set global view data
     */
    public static function share($key, $value = null)
    {
        if (is_array($key)) {
            self::$data = array_merge(self::$data, $key);
        } else {
            self::$data[$key] = $value;
        }
    }

    /**
     * Get shared data
     */
    public static function getShared($key = null)
    {
        if ($key === null) {
            return self::$data;
        }

        return self::$data[$key] ?? null;
    }

    /**
     * Include a partial view
     */
    public static function include($view, $data = [])
    {
        $data = array_merge(self::$data, $data);
        extract($data);

        $viewFile = self::findViewFile($view);

        if ($viewFile) {
            include $viewFile;
        } else {
            if (DEBUG_MODE) {
                echo "<!-- Partial view not found: {$view} -->";
            }
        }
    }

    /**
     * Start a section
     */
    public static function section($name)
    {
        self::$currentSection = $name;
        ob_start();
    }

    /**
     * End current section
     */
    public static function endSection()
    {
        if (self::$currentSection) {
            self::$sections[self::$currentSection] = ob_get_clean();
            self::$currentSection = null;
        }
    }

    /**
     * Yield section content
     */
    public static function yield($section, $default = '')
    {
        return self::$sections[$section] ?? $default;
    }

    /**
     * Check if section exists
     */
    public static function hasSection($section)
    {
        return isset(self::$sections[$section]);
    }

    /**
     * Escape HTML
     */
    public static function escape($value)
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public static function url($path = '')
    {
        return app_url($path);
    }

    /**
     * Generate asset URL
     */
    public static function asset($path = '')
    {
        return asset_url($path);
    }

    /**
     * Generate upload URL
     */
    public static function upload($path = '')
    {
        return upload_url($path);
    }

    /**
     * Format date
     */
    public static function date($date, $format = null)
    {
        $format = $format ?: DISPLAY_DATE_FORMAT;

        if (is_string($date)) {
            $date = new DateTime($date);
        }

        return $date->format($format);
    }

    /**
     * Format number
     */
    public static function number($number, $decimals = 0)
    {
        return number_format($number, $decimals);
    }

    /**
     * Format currency
     */
    public static function currency($amount, $currency = 'BDT')
    {
        return $currency . ' ' . number_format($amount, 2);
    }

    /**
     * Truncate text
     */
    public static function truncate($text, $length = 100, $suffix = '...')
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . $suffix;
    }

    /**
     * Generate CSRF token field
     */
    public static function csrf()
    {
        $token = self::generateCsrfToken();
        return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . $token . '">';
    }

    /**
     * Generate CSRF token
     */
    private static function generateCsrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }

        return $_SESSION['csrf_token'];
    }

    /**
     * Render error view
     */
    public static function renderError($message, $code = 500)
    {
        $errorView = "errors/{$code}";

        if (self::findViewFile($errorView)) {
            return self::render($errorView, ['message' => $message]);
        }

        // Fallback error template
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error {$code}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 50px; }
                .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='error'>
                <h1>Error {$code}</h1>
                <p>{$message}</p>
            </div>
        </body>
        </html>";
    }

    /**
     * Clear all data and sections
     */
    public static function clear()
    {
        self::$data = [];
        self::$sections = [];
        self::$currentSection = null;
    }
}