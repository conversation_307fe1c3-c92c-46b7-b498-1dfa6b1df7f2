<?php
/**
 * JobSpace - Main Application Class
 * Professional Application Bootstrap
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class App
{
    private static $instance = null;
    private $config = [];
    private $request;
    private $response;
    private $router;
    private $db;
    private $booted = false;

    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->loadConfiguration();
        $this->initializeCore();
    }

    /**
     * Get application instance (Singleton)
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Boot the application
     */
    public function boot()
    {
        if ($this->booted) {
            return $this;
        }

        try {
            // Start session
            $this->startSession();

            // Set error handling
            $this->setErrorHandling();

            // Initialize database
            $this->initializeDatabase();

            // Initialize view system
            $this->initializeView();

            // Load routes
            $this->loadRoutes();

            // Set global view data
            $this->setGlobalViewData();

            $this->booted = true;

        } catch (Exception $e) {
            $this->handleBootError($e);
        }

        return $this;
    }

    /**
     * Run the application
     */
    public function run()
    {
        try {
            // Boot application if not already booted
            if (!$this->booted) {
                $this->boot();
            }

            // Create request instance
            $this->request = new Request();

            // Dispatch request through router
            $response = Router::dispatch($this->request);

            // Send response
            if ($response instanceof Response) {
                $response->send();
            } else {
                echo $response;
            }

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * Load configuration files
     */
    private function loadConfiguration()
    {
        $this->config = [
            'app' => require CONFIG_PATH . '/app.php',
            'database' => require CONFIG_PATH . '/database.php',
        ];
    }

    /**
     * Initialize core components
     */
    private function initializeCore()
    {
        // Set timezone
        date_default_timezone_set($this->config['app']['timezone']);

        // Set locale
        setlocale(LC_ALL, $this->config['app']['locale']['default']);
    }

    /**
     * Start session
     */
    private function startSession()
    {
        if (session_status() === PHP_SESSION_NONE) {
            $sessionConfig = $this->config['app']['session'];

            // Configure session
            ini_set('session.name', $sessionConfig['name']);
            ini_set('session.gc_maxlifetime', $sessionConfig['lifetime'] * 60);
            ini_set('session.cookie_lifetime', $sessionConfig['lifetime'] * 60);
            ini_set('session.cookie_httponly', $sessionConfig['cookie']['http_only']);
            ini_set('session.cookie_secure', $sessionConfig['cookie']['secure']);
            ini_set('session.cookie_samesite', $sessionConfig['cookie']['same_site']);

            session_start();
        }
    }

    /**
     * Set error handling
     */
    private function setErrorHandling()
    {
        $debug = $this->config['app']['debug'];

        // Set error reporting
        if ($debug) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }

        // Set custom error handler
        set_error_handler([$this, 'handlePhpError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Initialize database
     */
    private function initializeDatabase()
    {
        try {
            $this->db = Database::getInstance();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                throw $e;
            } else {
                error_log("Database initialization failed: " . $e->getMessage());
            }
        }
    }

    /**
     * Initialize view system
     */
    private function initializeView()
    {
        View::init();
    }

    /**
     * Load application routes
     */
    private function loadRoutes()
    {
        $routeFiles = [
            ROUTES_PATH . '/web.php',
            ROUTES_PATH . '/api.php',
            ROUTES_PATH . '/auth.php',
            ROUTES_PATH . '/admin.php',
        ];

        foreach ($routeFiles as $file) {
            if (file_exists($file)) {
                require $file;
            }
        }
    }

    /**
     * Set global view data
     */
    private function setGlobalViewData()
    {
        View::share([
            'app' => $this,
            'config' => $this->config,
            'baseUrl' => BASE_URL,
            'assetUrl' => ASSETS_URL,
            'appName' => APP_NAME,
            'appVersion' => APP_VERSION,
        ]);
    }

    /**
     * Get configuration value
     */
    public function config($key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }

        return $value;
    }

    /**
     * Get request instance
     */
    public function request()
    {
        return $this->request;
    }

    /**
     * Get database instance
     */
    public function db()
    {
        return $this->db;
    }

    /**
     * Check if application is in debug mode
     */
    public function isDebug()
    {
        return $this->config('app.debug', false);
    }

    /**
     * Get application environment
     */
    public function environment()
    {
        return $this->config('app.environment', 'production');
    }

    /**
     * Check if running in specific environment
     */
    public function isEnvironment($env)
    {
        return $this->environment() === $env;
    }

    /**
     * Handle boot errors
     */
    private function handleBootError(Exception $e)
    {
        if (DEBUG_MODE) {
            $this->displayError($e);
        } else {
            error_log("Application boot error: " . $e->getMessage());
            $this->displayGenericError();
        }
        exit(1);
    }

    /**
     * Handle application errors
     */
    private function handleError(Exception $e)
    {
        http_response_code(500);

        if (DEBUG_MODE) {
            $this->displayError($e);
        } else {
            error_log("Application error: " . $e->getMessage());
            $this->displayGenericError();
        }
    }

    /**
     * Handle PHP errors
     */
    public function handlePhpError($severity, $message, $file, $line)
    {
        if (!(error_reporting() & $severity)) {
            return false;
        }

        $error = "PHP Error: {$message} in {$file} on line {$line}";

        if (DEBUG_MODE) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border-radius: 5px;'>";
            echo "<strong>PHP Error:</strong> {$message}<br>";
            echo "<strong>File:</strong> {$file}<br>";
            echo "<strong>Line:</strong> {$line}";
            echo "</div>";
        } else {
            error_log($error);
        }

        return true;
    }

    /**
     * Handle uncaught exceptions
     */
    public function handleException(Exception $e)
    {
        $this->handleError($e);
    }

    /**
     * Handle fatal errors
     */
    public function handleShutdown()
    {
        $error = error_get_last();

        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $message = "Fatal Error: {$error['message']} in {$error['file']} on line {$error['line']}";

            if (DEBUG_MODE) {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border-radius: 5px;'>";
                echo "<strong>Fatal Error:</strong> {$error['message']}<br>";
                echo "<strong>File:</strong> {$error['file']}<br>";
                echo "<strong>Line:</strong> {$error['line']}";
                echo "</div>";
            } else {
                error_log($message);
                $this->displayGenericError();
            }
        }
    }

    /**
     * Display detailed error (debug mode)
     */
    private function displayError(Exception $e)
    {
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Application Error</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; margin: 10px 0; }
                .trace { background: #f1f1f1; padding: 15px; border-radius: 5px; margin: 10px 0; overflow-x: auto; }
                pre { margin: 0; white-space: pre-wrap; }
            </style>
        </head>
        <body>
            <div class='error'>
                <h2>Application Error</h2>
                <p><strong>Message:</strong> {$e->getMessage()}</p>
                <p><strong>File:</strong> {$e->getFile()}</p>
                <p><strong>Line:</strong> {$e->getLine()}</p>
            </div>
            <div class='trace'>
                <h3>Stack Trace:</h3>
                <pre>{$e->getTraceAsString()}</pre>
            </div>
        </body>
        </html>";
    }

    /**
     * Display generic error (production mode)
     */
    private function displayGenericError()
    {
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Server Error</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
                .error { color: #721c24; }
            </style>
        </head>
        <body>
            <div class='error'>
                <h1>500 - Internal Server Error</h1>
                <p>Something went wrong. Please try again later.</p>
                <a href='/'>Go Home</a>
            </div>
        </body>
        </html>";
    }

    /**
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}