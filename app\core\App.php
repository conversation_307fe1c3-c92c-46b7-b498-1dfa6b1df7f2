<?php
/**
 * JobSpace - Application Core
 * Professional Application Class
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class App
{
    /**
     * Application instance
     */
    private static $instance = null;

    /**
     * Application booted status
     */
    private $booted = false;

    /**
     * Request instance
     */
    private $request;

    /**
     * Response instance
     */
    private $response;

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->boot();
    }

    /**
     * Get application instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Boot the application
     */
    private function boot()
    {
        try {
            // Start session
            $this->startSession();

            // Set error handling
            $this->setErrorHandling();

            // Load routes
            $this->loadRoutes();

            $this->booted = true;

        } catch (Exception $e) {
            $this->handleBootError($e);
        }
    }

    /**
     * Start session
     */
    private function startSession()
    {
        if (session_status() === PHP_SESSION_NONE) {
            // Configure session
            ini_set('session.name', SESSION_NAME);
            ini_set('session.gc_maxlifetime', 7200); // 2 hours
            ini_set('session.cookie_lifetime', 7200);
            ini_set('session.cookie_httponly', true);
            ini_set('session.cookie_secure', false); // Set to true for HTTPS
            ini_set('session.cookie_samesite', 'Lax');

            session_start();
        }
    }

    /**
     * Set error handling
     */
    private function setErrorHandling()
    {
        if (DEBUG_MODE) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }

        // Set custom error handler
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
    }

    /**
     * Load application routes
     */
    private function loadRoutes()
    {
        $routeFiles = [
            ROUTES_PATH . '/web.php'
        ];

        foreach ($routeFiles as $file) {
            if (file_exists($file)) {
                require_once $file;
            }
        }
    }

    /**
     * Run the application
     */
    public function run()
    {
        if (!$this->booted) {
            throw new Exception('Application not booted');
        }

        try {
            // Create request and response instances
            $this->request = new Request();
            $this->response = new Response();

            // Dispatch request through router
            Router::dispatch($this->request);

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Handle boot errors
     */
    private function handleBootError(Exception $e)
    {
        if (DEBUG_MODE) {
            die("Application Boot Error: " . $e->getMessage());
        } else {
            error_log("Application Boot Error: " . $e->getMessage());
            die("Application failed to start. Please try again later.");
        }
    }

    /**
     * Handle errors
     */
    public function handleError($severity, $message, $file, $line)
    {
        if (!(error_reporting() & $severity)) {
            return false;
        }

        $error = "Error [{$severity}]: {$message} in {$file} on line {$line}";

        if (DEBUG_MODE) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<strong>Error:</strong> " . htmlspecialchars($error);
            echo "</div>";
        } else {
            error_log($error);
        }

        return true;
    }

    /**
     * Handle exceptions
     */
    public function handleException(Exception $e)
    {
        if (DEBUG_MODE) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<h3>Exception:</h3>";
            echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
            echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
            echo "</div>";
        } else {
            error_log("Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

            // Show user-friendly error page
            if (isset($this->response)) {
                $this->response->serverError('An error occurred. Please try again later.');
            } else {
                echo "An error occurred. Please try again later.";
            }
        }
    }

    /**
     * Get request instance
     */
    public function getRequest()
    {
        return $this->request;
    }

    /**
     * Get response instance
     */
    public function getResponse()
    {
        return $this->response;
    }

    /**
     * Check if application is booted
     */
    public function isBooted()
    {
        return $this->booted;
    }

    /**
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}