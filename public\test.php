<?php
/**
 * JobSpace - System Test Page
 * Debug and troubleshoot system issues
 */

echo "<h1>JobSpace System Test</h1>";
echo "<hr>";

// 1. Check basic PHP info
echo "<h2>1. PHP Environment</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<hr>";

// 2. Check file paths
echo "<h2>2. File System Check</h2>";
$files_to_check = [
    'Bootstrap App' => __DIR__ . '/../bootstrap/app.php',
    'Constants' => __DIR__ . '/../config/constants.php',
    'Database Config' => __DIR__ . '/../config/database.php',
    'App Config' => __DIR__ . '/../config/app.php',
    'Autoload' => __DIR__ . '/../bootstrap/autoload.php',
    'Web Routes' => __DIR__ . '/../routes/web.php',
    'Home Controller' => __DIR__ . '/../app/controllers/home/<USER>',
    'Core App' => __DIR__ . '/../app/core/App.php',
    'Core Router' => __DIR__ . '/../app/core/Router.php',
    'Core Request' => __DIR__ . '/../app/core/Request.php',
    'Core Database' => __DIR__ . '/../app/core/Database.php',
    'Home View' => __DIR__ . '/../views/pages/home.php',
];

foreach ($files_to_check as $name => $path) {
    $exists = file_exists($path);
    $readable = $exists ? is_readable($path) : false;
    echo "<p><strong>{$name}:</strong> ";
    echo $exists ? "✅ Exists" : "❌ Missing";
    echo $readable ? " & Readable" : ($exists ? " but Not Readable" : "");
    echo " <small>({$path})</small></p>";
}
echo "<hr>";

// 3. Test constants loading
echo "<h2>3. Constants Test</h2>";
try {
    define('JOBSPACE_ACCESS', true);
    require_once __DIR__ . '/../config/constants.php';
    
    echo "<p><strong>ROOT_PATH:</strong> " . (defined('ROOT_PATH') ? ROOT_PATH : 'Not defined') . "</p>";
    echo "<p><strong>APP_PATH:</strong> " . (defined('APP_PATH') ? APP_PATH : 'Not defined') . "</p>";
    echo "<p><strong>VIEWS_PATH:</strong> " . (defined('VIEWS_PATH') ? VIEWS_PATH : 'Not defined') . "</p>";
    echo "<p><strong>BASE_URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
    echo "<p><strong>APP_NAME:</strong> " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error loading constants: " . $e->getMessage() . "</p>";
}
echo "<hr>";

// 4. Test autoloader
echo "<h2>4. Autoloader Test</h2>";
try {
    require_once __DIR__ . '/../bootstrap/autoload.php';
    echo "<p>✅ Autoloader loaded successfully</p>";
    
    // Test class loading
    $classes_to_test = ['Database', 'Request', 'Response', 'Router', 'App'];
    foreach ($classes_to_test as $class) {
        if (class_exists($class)) {
            echo "<p>✅ Class {$class} loaded</p>";
        } else {
            echo "<p>❌ Class {$class} not found</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading autoloader: " . $e->getMessage() . "</p>";
}
echo "<hr>";

// 5. Test routing
echo "<h2>5. Routing Test</h2>";
try {
    require_once __DIR__ . '/../routes/web.php';
    echo "<p>✅ Routes loaded successfully</p>";
    
    // Check if Router class has routes
    if (class_exists('Router')) {
        echo "<p>✅ Router class available</p>";
    } else {
        echo "<p>❌ Router class not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading routes: " . $e->getMessage() . "</p>";
}
echo "<hr>";

// 6. Test controller
echo "<h2>6. Controller Test</h2>";
try {
    if (file_exists(__DIR__ . '/../app/controllers/home/<USER>')) {
        require_once __DIR__ . '/../app/controllers/home/<USER>';
        
        if (class_exists('HomeController')) {
            echo "<p>✅ HomeController class loaded</p>";
            
            // Try to instantiate
            $controller = new HomeController();
            echo "<p>✅ HomeController instantiated</p>";
            
        } else {
            echo "<p>❌ HomeController class not found after include</p>";
        }
    } else {
        echo "<p>❌ HomeController file not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error testing controller: " . $e->getMessage() . "</p>";
}
echo "<hr>";

// 7. URL Test
echo "<h2>7. URL Configuration Test</h2>";
echo "<p><strong>Detected Base URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not detected') . "</p>";
echo "<p><strong>Expected URLs:</strong></p>";
echo "<ul>";
echo "<li>Home: <a href='" . (defined('BASE_URL') ? BASE_URL : '') . "/' target='_blank'>" . (defined('BASE_URL') ? BASE_URL : '') . "/</a></li>";
echo "<li>Test: <a href='" . (defined('BASE_URL') ? BASE_URL : '') . "/test.php' target='_blank'>" . (defined('BASE_URL') ? BASE_URL : '') . "/test.php</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
