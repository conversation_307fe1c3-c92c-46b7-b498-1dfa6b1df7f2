<?php
/**
 * JobSpace - HTTP Request Handler
 * Professional Request Class
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class Request
{
    /**
     * Request method
     */
    private $method;

    /**
     * Request URI
     */
    private $uri;

    /**
     * Request parameters
     */
    private $params = [];

    /**
     * GET data
     */
    private $get = [];

    /**
     * POST data
     */
    private $post = [];

    /**
     * FILES data
     */
    private $files = [];

    /**
     * Headers
     */
    private $headers = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->method = $this->parseMethod();
        $this->uri = $this->parseUri();
        $this->get = $_GET;
        $this->post = $_POST;
        $this->files = $_FILES;
        $this->headers = $this->parseHeaders();
    }

    /**
     * Parse request method
     */
    private function parseMethod()
    {
        return strtoupper($_SERVER['REQUEST_METHOD'] ?? 'GET');
    }

    /**
     * Parse URI from request
     */
    private function parseUri()
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';

        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }

        // Remove base path for subdirectory installations
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = '';

        // Extract base path
        if (strpos($scriptName, '/public/index.php') !== false) {
            $basePath = str_replace('/public/index.php', '', $scriptName);
        } elseif (strpos($scriptName, '/index.php') !== false) {
            $basePath = str_replace('/index.php', '', $scriptName);
        } else {
            $basePath = dirname($scriptName);
            if ($basePath === '/' || $basePath === '\\') {
                $basePath = '';
            }
        }

        // Remove base path from URI
        if ($basePath && $basePath !== '/' && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
        }

        // Ensure URI starts with /
        if (!$uri || $uri[0] !== '/') {
            $uri = '/' . $uri;
        }

        // Clean up trailing slashes but keep root /
        $uri = rtrim($uri, '/');
        return $uri ?: '/';
    }

    /**
     * Parse headers
     */
    private function parseHeaders()
    {
        $headers = [];

        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $key => $value) {
                if (strpos($key, 'HTTP_') === 0) {
                    $header = str_replace('_', '-', substr($key, 5));
                    $headers[$header] = $value;
                }
            }
        }

        return $headers;
    }

    /**
     * Get request method
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * Get request URI
     */
    public function getUri()
    {
        return $this->uri;
    }

    /**
     * Get base URL
     */
    public function getBaseUrl()
    {
        return BASE_URL;
    }

    /**
     * Set route parameters
     */
    public function setParams($params)
    {
        $this->params = $params;
    }

    /**
     * Get route parameters
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * Get specific parameter
     */
    public function getParam($key, $default = null)
    {
        return $this->params[$key] ?? $default;
    }

    /**
     * Get GET data
     */
    public function get($key = null, $default = null)
    {
        if ($key === null) {
            return $this->get;
        }

        return $this->get[$key] ?? $default;
    }

    /**
     * Get POST data
     */
    public function post($key = null, $default = null)
    {
        if ($key === null) {
            return $this->post;
        }

        return $this->post[$key] ?? $default;
    }

    /**
     * Get input data (GET or POST)
     */
    public function input($key = null, $default = null)
    {
        if ($key === null) {
            return array_merge($this->get, $this->post);
        }

        return $this->post[$key] ?? $this->get[$key] ?? $default;
    }

    /**
     * Get FILES data
     */
    public function files($key = null)
    {
        if ($key === null) {
            return $this->files;
        }

        return $this->files[$key] ?? null;
    }

    /**
     * Get headers
     */
    public function headers($key = null)
    {
        if ($key === null) {
            return $this->headers;
        }

        return $this->headers[$key] ?? null;
    }

    /**
     * Check if request is AJAX
     */
    public function isAjax()
    {
        return strtolower($this->headers('X-Requested-With') ?? '') === 'xmlhttprequest';
    }

    /**
     * Check if request is POST
     */
    public function isPost()
    {
        return $this->method === 'POST';
    }

    /**
     * Check if request is GET
     */
    public function isGet()
    {
        return $this->method === 'GET';
    }

    /**
     * Check if request is secure (HTTPS)
     */
    public function isSecure()
    {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               ($_SERVER['SERVER_PORT'] ?? 80) == 443;
    }

    /**
     * Get client IP address
     */
    public function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Get user agent
     */
    public function getUserAgent()
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
}