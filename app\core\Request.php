<?php
/**
 * JobSpace - Request Core Class
 * Professional HTTP Request Handling
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class Request
{
    private $method;
    private $uri;
    private $params;
    private $query;
    private $body;
    private $headers;
    private $files;
    private $server;
    private $cookies;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $this->uri = $this->parseUri();
        $this->params = [];
        $this->query = $_GET ?? [];
        $this->body = $this->parseBody();
        $this->headers = $this->parseHeaders();
        $this->files = $_FILES ?? [];
        $this->server = $_SERVER ?? [];
        $this->cookies = $_COOKIE ?? [];
    }

    /**
     * Get HTTP method
     */
    public function getMethod()
    {
        return strtoupper($this->method);
    }

    /**
     * Check if method is GET
     */
    public function isGet()
    {
        return $this->getMethod() === 'GET';
    }

    /**
     * Check if method is POST
     */
    public function isPost()
    {
        return $this->getMethod() === 'POST';
    }

    /**
     * Check if method is PUT
     */
    public function isPut()
    {
        return $this->getMethod() === 'PUT';
    }

    /**
     * Check if method is DELETE
     */
    public function isDelete()
    {
        return $this->getMethod() === 'DELETE';
    }

    /**
     * Check if method is PATCH
     */
    public function isPatch()
    {
        return $this->getMethod() === 'PATCH';
    }

    /**
     * Check if request is AJAX
     */
    public function isAjax()
    {
        return !empty($this->server['HTTP_X_REQUESTED_WITH']) &&
               strtolower($this->server['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Check if request is secure (HTTPS)
     */
    public function isSecure()
    {
        return !empty($this->server['HTTPS']) && $this->server['HTTPS'] !== 'off';
    }

    /**
     * Get request URI
     */
    public function getUri()
    {
        return $this->uri;
    }

    /**
     * Get full URL
     */
    public function getUrl()
    {
        $protocol = $this->isSecure() ? 'https' : 'http';
        $host = $this->server['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . $this->server['REQUEST_URI'];
    }

    /**
     * Get base URL
     */
    public function getBaseUrl()
    {
        $protocol = $this->isSecure() ? 'https' : 'http';
        $host = $this->server['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }

    /**
     * Parse URI from request
     */
    private function parseUri()
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';

        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }

        // Remove base path for subdirectory installations
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = str_replace('/public/index.php', '', $scriptName);
        $basePath = str_replace('/index.php', '', $basePath);

        if ($basePath && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
        }

        // Ensure URI starts with /
        if (!$uri || $uri[0] !== '/') {
            $uri = '/' . $uri;
        }

        return rtrim($uri, '/') ?: '/';
    }

    /**
     * Parse request body
     */
    private function parseBody()
    {
        $body = [];

        if ($this->isPost()) {
            $body = $_POST ?? [];
        } else {
            // Parse JSON or other content types
            $contentType = $this->getHeader('Content-Type');

            if (strpos($contentType, 'application/json') !== false) {
                $input = file_get_contents('php://input');
                $decoded = json_decode($input, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $body = $decoded;
                }
            }
        }

        return $body;
    }

    /**
     * Parse request headers
     */
    private function parseHeaders()
    {
        $headers = [];

        foreach ($this->server as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('_', '-', substr($key, 5));
                $header = ucwords(strtolower($header), '-');
                $headers[$header] = $value;
            }
        }

        return $headers;
    }

    /**
     * Get input value
     */
    public function input($key = null, $default = null)
    {
        if ($key === null) {
            return array_merge($this->query, $this->body);
        }

        // Check body first, then query
        if (isset($this->body[$key])) {
            return $this->body[$key];
        }

        if (isset($this->query[$key])) {
            return $this->query[$key];
        }

        return $default;
    }

    /**
     * Get query parameter
     */
    public function query($key = null, $default = null)
    {
        if ($key === null) {
            return $this->query;
        }

        return $this->query[$key] ?? $default;
    }

    /**
     * Get POST data
     */
    public function post($key = null, $default = null)
    {
        if ($key === null) {
            return $this->body;
        }

        return $this->body[$key] ?? $default;
    }

    /**
     * Get header value
     */
    public function getHeader($name, $default = null)
    {
        $name = ucwords(strtolower($name), '-');
        return $this->headers[$name] ?? $default;
    }

    /**
     * Get all headers
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * Get uploaded file
     */
    public function file($key)
    {
        return $this->files[$key] ?? null;
    }

    /**
     * Get all uploaded files
     */
    public function files()
    {
        return $this->files;
    }

    /**
     * Check if file was uploaded
     */
    public function hasFile($key)
    {
        return isset($this->files[$key]) && $this->files[$key]['error'] === UPLOAD_ERR_OK;
    }

    /**
     * Get cookie value
     */
    public function cookie($key, $default = null)
    {
        return $this->cookies[$key] ?? $default;
    }

    /**
     * Get server variable
     */
    public function server($key, $default = null)
    {
        return $this->server[$key] ?? $default;
    }

    /**
     * Get client IP address
     */
    public function getClientIp()
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (!empty($this->server[$key])) {
                $ip = trim($this->server[$key]);

                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $this->server['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Get user agent
     */
    public function getUserAgent()
    {
        return $this->server['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * Set route parameters
     */
    public function setParams($params)
    {
        $this->params = $params;
    }

    /**
     * Get route parameter
     */
    public function param($key, $default = null)
    {
        return $this->params[$key] ?? $default;
    }

    /**
     * Get all route parameters
     */
    public function params()
    {
        return $this->params;
    }

    /**
     * Validate input data
     */
    public function validate($rules)
    {
        $errors = [];
        $data = $this->input();

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;

            foreach ($ruleList as $singleRule) {
                if ($singleRule === 'required' && empty($value)) {
                    $errors[$field][] = "The {$field} field is required.";
                } elseif ($singleRule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "The {$field} must be a valid email address.";
                } elseif (strpos($singleRule, 'min:') === 0) {
                    $min = (int) substr($singleRule, 4);
                    if (strlen($value) < $min) {
                        $errors[$field][] = "The {$field} must be at least {$min} characters.";
                    }
                } elseif (strpos($singleRule, 'max:') === 0) {
                    $max = (int) substr($singleRule, 4);
                    if (strlen($value) > $max) {
                        $errors[$field][] = "The {$field} may not be greater than {$max} characters.";
                    }
                }
            }
        }

        return $errors;
    }
}