<?php
/**
 * JobSpace - HTTP Response Handler
 * Professional Response Class
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class Response
{
    /**
     * HTTP status code
     */
    private $statusCode = 200;

    /**
     * Response headers
     */
    private $headers = [];

    /**
     * Response content
     */
    private $content = '';

    /**
     * HTTP status codes
     */
    private static $statusTexts = [
        200 => 'OK',
        201 => 'Created',
        204 => 'No Content',
        301 => 'Moved Permanently',
        302 => 'Found',
        304 => 'Not Modified',
        400 => 'Bad Request',
        401 => 'Unauthorized',
        403 => 'Forbidden',
        404 => 'Not Found',
        405 => 'Method Not Allowed',
        422 => 'Unprocessable Entity',
        500 => 'Internal Server Error',
        503 => 'Service Unavailable'
    ];

    /**
     * Set HTTP status code
     */
    public function setStatusCode($code)
    {
        $this->statusCode = $code;
        return $this;
    }

    /**
     * Get HTTP status code
     */
    public function getStatusCode()
    {
        return $this->statusCode;
    }

    /**
     * Set header
     */
    public function setHeader($name, $value)
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Set multiple headers
     */
    public function setHeaders($headers)
    {
        foreach ($headers as $name => $value) {
            $this->setHeader($name, $value);
        }
        return $this;
    }

    /**
     * Get header
     */
    public function getHeader($name)
    {
        return $this->headers[$name] ?? null;
    }

    /**
     * Get all headers
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * Set content
     */
    public function setContent($content)
    {
        $this->content = $content;
        return $this;
    }

    /**
     * Get content
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Send response
     */
    public function send()
    {
        // Send status code
        $statusText = self::$statusTexts[$this->statusCode] ?? 'Unknown';
        header("HTTP/1.1 {$this->statusCode} {$statusText}");

        // Send headers
        foreach ($this->headers as $name => $value) {
            header("{$name}: {$value}");
        }

        // Send content
        echo $this->content;

        return $this;
    }

    /**
     * Send JSON response
     */
    public function json($data, $statusCode = 200)
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Content-Type', 'application/json')
             ->setContent(json_encode($data))
             ->send();

        return $this;
    }

    /**
     * Send HTML response
     */
    public function html($content, $statusCode = 200)
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Content-Type', 'text/html; charset=UTF-8')
             ->setContent($content)
             ->send();

        return $this;
    }

    /**
     * Redirect to URL
     */
    public function redirect($url, $statusCode = 302)
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Location', $url)
             ->send();

        exit;
    }

    /**
     * Redirect back
     */
    public function back()
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL;
        $this->redirect($referer);
    }

    /**
     * Send 404 Not Found
     */
    public function notFound($message = 'Page not found')
    {
        $this->setStatusCode(404)
             ->setHeader('Content-Type', 'text/html; charset=UTF-8')
             ->setContent($this->render404Page($message))
             ->send();

        return $this;
    }

    /**
     * Send 500 Internal Server Error
     */
    public function serverError($message = 'Internal server error')
    {
        $this->setStatusCode(500)
             ->setHeader('Content-Type', 'text/html; charset=UTF-8')
             ->setContent($this->render500Page($message))
             ->send();

        return $this;
    }

    /**
     * Send 403 Forbidden
     */
    public function forbidden($message = 'Access forbidden')
    {
        $this->setStatusCode(403)
             ->setHeader('Content-Type', 'text/html; charset=UTF-8')
             ->setContent($this->render403Page($message))
             ->send();

        return $this;
    }

    /**
     * Render 404 page
     */
    private function render404Page($message)
    {
        $errorPage = VIEWS_PATH . '/errors/404.php';

        if (file_exists($errorPage)) {
            ob_start();
            include $errorPage;
            return ob_get_clean();
        }

        return $this->renderSimpleErrorPage('404 - Page Not Found', $message);
    }

    /**
     * Render 500 page
     */
    private function render500Page($message)
    {
        $errorPage = VIEWS_PATH . '/errors/500.php';

        if (file_exists($errorPage)) {
            ob_start();
            include $errorPage;
            return ob_get_clean();
        }

        return $this->renderSimpleErrorPage('500 - Server Error', $message);
    }

    /**
     * Render 403 page
     */
    private function render403Page($message)
    {
        $errorPage = VIEWS_PATH . '/errors/403.php';

        if (file_exists($errorPage)) {
            ob_start();
            include $errorPage;
            return ob_get_clean();
        }

        return $this->renderSimpleErrorPage('403 - Access Forbidden', $message);
    }

    /**
     * Render simple error page
     */
    private function renderSimpleErrorPage($title, $message)
    {
        return "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>{$title} - JobSpace</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #e74c3c; margin-bottom: 20px; }
        p { color: #666; margin-bottom: 30px; }
        a { color: #3498db; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class='error-container'>
        <h1>{$title}</h1>
        <p>{$message}</p>
        <a href='" . BASE_URL . "'>← Back to Home</a>
    </div>
</body>
</html>";
    }

    /**
     * Set cache headers
     */
    public function cache($seconds = 3600)
    {
        $this->setHeader('Cache-Control', 'public, max-age=' . $seconds)
             ->setHeader('Expires', gmdate('D, d M Y H:i:s', time() + $seconds) . ' GMT');

        return $this;
    }

    /**
     * Set no-cache headers
     */
    public function noCache()
    {
        $this->setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
             ->setHeader('Pragma', 'no-cache')
             ->setHeader('Expires', '0');

        return $this;
    }
}