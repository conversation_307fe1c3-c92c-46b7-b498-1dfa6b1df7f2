<?php
/**
 * JobSpace - Response Core Class
 * Professional HTTP Response Handling
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class Response
{
    private $statusCode = 200;
    private $headers = [];
    private $content = '';
    private $contentType = 'text/html';

    /**
     * HTTP Status Codes
     */
    private static $statusTexts = [
        200 => 'OK',
        201 => 'Created',
        204 => 'No Content',
        301 => 'Moved Permanently',
        302 => 'Found',
        304 => 'Not Modified',
        400 => 'Bad Request',
        401 => 'Unauthorized',
        403 => 'Forbidden',
        404 => 'Not Found',
        405 => 'Method Not Allowed',
        422 => 'Unprocessable Entity',
        500 => 'Internal Server Error',
        502 => 'Bad Gateway',
        503 => 'Service Unavailable',
    ];

    /**
     * Set HTTP status code
     */
    public function setStatusCode($code)
    {
        $this->statusCode = $code;
        return $this;
    }

    /**
     * Get HTTP status code
     */
    public function getStatusCode()
    {
        return $this->statusCode;
    }

    /**
     * Set response header
     */
    public function setHeader($name, $value)
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Set multiple headers
     */
    public function setHeaders($headers)
    {
        foreach ($headers as $name => $value) {
            $this->setHeader($name, $value);
        }
        return $this;
    }

    /**
     * Get response header
     */
    public function getHeader($name)
    {
        return $this->headers[$name] ?? null;
    }

    /**
     * Get all headers
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * Set content type
     */
    public function setContentType($type)
    {
        $this->contentType = $type;
        $this->setHeader('Content-Type', $type);
        return $this;
    }

    /**
     * Set response content
     */
    public function setContent($content)
    {
        $this->content = $content;
        return $this;
    }

    /**
     * Get response content
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * Send JSON response
     */
    public function json($data, $statusCode = 200)
    {
        $this->setStatusCode($statusCode)
             ->setContentType('application/json')
             ->setContent(json_encode($data, JSON_UNESCAPED_UNICODE));

        return $this->send();
    }

    /**
     * Send HTML response
     */
    public function html($content, $statusCode = 200)
    {
        $this->setStatusCode($statusCode)
             ->setContentType('text/html; charset=UTF-8')
             ->setContent($content);

        return $this->send();
    }

    /**
     * Send plain text response
     */
    public function text($content, $statusCode = 200)
    {
        $this->setStatusCode($statusCode)
             ->setContentType('text/plain; charset=UTF-8')
             ->setContent($content);

        return $this->send();
    }

    /**
     * Redirect to URL
     */
    public function redirect($url, $statusCode = 302)
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Location', $url);

        return $this->send();
    }

    /**
     * Redirect back to previous page
     */
    public function back()
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        return $this->redirect($referer);
    }

    /**
     * Send file download
     */
    public function download($filePath, $fileName = null, $headers = [])
    {
        if (!file_exists($filePath)) {
            return $this->setStatusCode(404)->send();
        }

        $fileName = $fileName ?: basename($filePath);
        $fileSize = filesize($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        $this->setHeaders([
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Content-Length' => $fileSize,
            'Cache-Control' => 'must-revalidate',
            'Pragma' => 'public',
            'Expires' => '0'
        ]);

        // Set additional headers
        $this->setHeaders($headers);

        // Send headers
        $this->sendHeaders();

        // Send file content
        readfile($filePath);

        return $this;
    }

    /**
     * Send file inline (for viewing)
     */
    public function file($filePath, $headers = [])
    {
        if (!file_exists($filePath)) {
            return $this->setStatusCode(404)->send();
        }

        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';
        $fileSize = filesize($filePath);

        $this->setHeaders([
            'Content-Type' => $mimeType,
            'Content-Length' => $fileSize,
            'Cache-Control' => 'public, max-age=3600',
        ]);

        // Set additional headers
        $this->setHeaders($headers);

        // Send headers
        $this->sendHeaders();

        // Send file content
        readfile($filePath);

        return $this;
    }

    /**
     * Set cache headers
     */
    public function cache($seconds = 3600)
    {
        $this->setHeaders([
            'Cache-Control' => 'public, max-age=' . $seconds,
            'Expires' => gmdate('D, d M Y H:i:s', time() + $seconds) . ' GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s', time()) . ' GMT'
        ]);

        return $this;
    }

    /**
     * Disable cache
     */
    public function noCache()
    {
        $this->setHeaders([
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);

        return $this;
    }

    /**
     * Set CORS headers
     */
    public function cors($origin = '*', $methods = 'GET, POST, PUT, DELETE, OPTIONS', $headers = 'Content-Type, Authorization')
    {
        $this->setHeaders([
            'Access-Control-Allow-Origin' => $origin,
            'Access-Control-Allow-Methods' => $methods,
            'Access-Control-Allow-Headers' => $headers,
            'Access-Control-Max-Age' => '86400'
        ]);

        return $this;
    }

    /**
     * Send response
     */
    public function send()
    {
        // Send status code
        http_response_code($this->statusCode);

        // Send headers
        $this->sendHeaders();

        // Send content
        echo $this->content;

        return $this;
    }

    /**
     * Send headers
     */
    private function sendHeaders()
    {
        if (headers_sent()) {
            return;
        }

        foreach ($this->headers as $name => $value) {
            header($name . ': ' . $value);
        }
    }

    /**
     * Create success response
     */
    public static function success($data = null, $message = 'Success', $statusCode = 200)
    {
        $response = new self();

        $responseData = [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];

        return $response->json($responseData, $statusCode);
    }

    /**
     * Create error response
     */
    public static function error($message = 'Error occurred', $errors = null, $statusCode = 400)
    {
        $response = new self();

        $responseData = [
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ];

        return $response->json($responseData, $statusCode);
    }

    /**
     * Create validation error response
     */
    public static function validationError($errors, $message = 'Validation failed')
    {
        return self::error($message, $errors, 422);
    }

    /**
     * Create not found response
     */
    public static function notFound($message = 'Resource not found')
    {
        return self::error($message, null, 404);
    }

    /**
     * Create unauthorized response
     */
    public static function unauthorized($message = 'Unauthorized access')
    {
        return self::error($message, null, 401);
    }

    /**
     * Create forbidden response
     */
    public static function forbidden($message = 'Access forbidden')
    {
        return self::error($message, null, 403);
    }

    /**
     * Create server error response
     */
    public static function serverError($message = 'Internal server error')
    {
        return self::error($message, null, 500);
    }
}