<?php
/**
 * JobSpace - Database Core Class
 * Professional Database Management
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class Database
{
    private static $instance = null;
    private $connection = null;
    private $config = [];
    private $queryLog = [];

    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->config = require CONFIG_PATH . '/database.php';
        $this->connect();
    }

    /**
     * Get database instance (Singleton)
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get database connection
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Connect to database
     */
    private function connect()
    {
        try {
            $defaultConnection = $this->config['default'];
            $connectionConfig = $this->config['connections'][$defaultConnection];

            if ($connectionConfig['driver'] === 'mysql') {
                $dsn = "mysql:host={$connectionConfig['host']};port={$connectionConfig['port']};dbname={$connectionConfig['database']};charset={$connectionConfig['charset']}";

                $this->connection = new PDO(
                    $dsn,
                    $connectionConfig['username'],
                    $connectionConfig['password'],
                    $connectionConfig['options']
                );

                // Set additional MySQL settings
                $this->connection->exec("SET sql_mode = '{$connectionConfig['options'][PDO::MYSQL_ATTR_INIT_COMMAND]}'");

            } elseif ($connectionConfig['driver'] === 'sqlite') {
                $dsn = "sqlite:{$connectionConfig['database']}";
                $this->connection = new PDO($dsn);
                $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            }

        } catch (PDOException $e) {
            $this->handleError("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Execute a query
     */
    public function query($sql, $params = [])
    {
        try {
            $startTime = microtime(true);

            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute($params);

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

            // Log query if enabled
            if ($this->config['query_log']['enabled']) {
                $this->logQuery($sql, $params, $executionTime);
            }

            return $stmt;

        } catch (PDOException $e) {
            $this->handleError("Query execution failed: " . $e->getMessage() . " | SQL: " . $sql);
            return false;
        }
    }

    /**
     * Fetch all results
     */
    public function fetchAll($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : [];
    }

    /**
     * Fetch single result
     */
    public function fetch($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : null;
    }

    /**
     * Insert data
     */
    public function insert($table, $data)
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";

        $stmt = $this->query($sql, $data);

        if ($stmt) {
            return $this->connection->lastInsertId();
        }

        return false;
    }

    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = [])
    {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setClause);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";

        $params = array_merge($data, $whereParams);
        $stmt = $this->query($sql, $params);

        return $stmt ? $stmt->rowCount() : false;
    }

    /**
     * Delete data
     */
    public function delete($table, $where, $whereParams = [])
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $whereParams);

        return $stmt ? $stmt->rowCount() : false;
    }

    /**
     * Get table columns
     */
    public function getColumns($table)
    {
        $sql = "DESCRIBE {$table}";
        return $this->fetchAll($sql);
    }

    /**
     * Check if table exists
     */
    public function tableExists($table)
    {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetch($sql, ['table' => $table]);
        return !empty($result);
    }

    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        return $this->connection->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit()
    {
        return $this->connection->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback()
    {
        return $this->connection->rollback();
    }

    /**
     * Log query for debugging
     */
    private function logQuery($sql, $params, $executionTime)
    {
        $logEntry = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->queryLog[] = $logEntry;

        // Log slow queries
        if ($executionTime > $this->config['query_log']['slow_query_threshold']) {
            $logMessage = "SLOW QUERY ({$executionTime}ms): {$sql} | Params: " . json_encode($params);
            error_log($logMessage, 3, $this->config['query_log']['log_file']);
        }
    }

    /**
     * Get query log
     */
    public function getQueryLog()
    {
        return $this->queryLog;
    }

    /**
     * Handle database errors
     */
    private function handleError($message)
    {
        if (DEBUG_MODE) {
            throw new Exception($message);
        } else {
            error_log($message, 3, LOGS_PATH . '/database_errors.log');
            throw new Exception('Database error occurred. Please try again later.');
        }
    }

    /**
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}