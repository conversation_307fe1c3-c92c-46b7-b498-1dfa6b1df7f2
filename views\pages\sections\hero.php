<!-- JobSpace - Hero Section -->
<section class="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');"></div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
    <div class="absolute top-40 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce"></div>
    <div class="absolute bottom-20 left-20 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping"></div>

    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

            <!-- Content -->
            <div class="text-center lg:text-left space-y-8">
                <div class="space-y-4">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                        Learn • Earn •
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                            Connect
                        </span> • Trade
                    </h1>
                    <p class="text-xl md:text-2xl text-blue-100 leading-relaxed">
                        The ultimate multi-platform ecosystem where education meets opportunity.
                        Join thousands of learners, freelancers, and entrepreneurs.
                    </p>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 py-6">
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-yellow-400">
                            <?= $stats['totalUsersFormatted'] ?? '1K+' ?>
                        </div>
                        <div class="text-sm text-blue-200">Active Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-green-400">
                            <?= $stats['totalQuizzesFormatted'] ?? '500+' ?>
                        </div>
                        <div class="text-sm text-blue-200">Quizzes</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-purple-400">
                            <?= $stats['totalJobsFormatted'] ?? '200+' ?>
                        </div>
                        <div class="text-sm text-blue-200">Jobs Posted</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-orange-400">
                            <?= $stats['totalProductsFormatted'] ?? '300+' ?>
                        </div>
                        <div class="text-sm text-blue-200">Products</div>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <?php if (isset($isAuthenticated) && $isAuthenticated): ?>
                        <a href="/feed"
                           class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Go to Dashboard
                        </a>
                        <a href="/quiz"
                           class="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-brain mr-2"></i>
                            Start Learning
                        </a>
                    <?php else: ?>
                        <a href="/register"
                           class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-rocket mr-2"></i>
                            Get Started Free
                        </a>
                        <a href="/login"
                           class="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign In
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap items-center justify-center lg:justify-start gap-6 pt-6">
                    <div class="flex items-center space-x-2 text-blue-200">
                        <i class="fas fa-shield-alt text-green-400"></i>
                        <span class="text-sm">100% Secure</span>
                    </div>
                    <div class="flex items-center space-x-2 text-blue-200">
                        <i class="fas fa-clock text-yellow-400"></i>
                        <span class="text-sm">24/7 Support</span>
                    </div>
                    <div class="flex items-center space-x-2 text-blue-200">
                        <i class="fas fa-mobile-alt text-purple-400"></i>
                        <span class="text-sm">Mobile Ready</span>
                    </div>
                </div>
            </div>

            <!-- Visual/Illustration -->
            <div class="relative">
                <!-- Main Illustration Container -->
                <div class="relative bg-white bg-opacity-10 rounded-3xl p-8 backdrop-blur-sm border border-white border-opacity-20">

                    <!-- Feature Cards -->
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Quiz Card -->
                        <div class="bg-white bg-opacity-20 rounded-xl p-4 text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-brain text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm mb-1">Interactive Quiz</h4>
                            <p class="text-xs text-blue-200">Learn & Earn</p>
                        </div>

                        <!-- Jobs Card -->
                        <div class="bg-white bg-opacity-20 rounded-xl p-4 text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-briefcase text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm mb-1">Find Jobs</h4>
                            <p class="text-xs text-blue-200">Freelance Work</p>
                        </div>

                        <!-- Social Card -->
                        <div class="bg-white bg-opacity-20 rounded-xl p-4 text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-users text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm mb-1">Connect</h4>
                            <p class="text-xs text-blue-200">Social Network</p>
                        </div>

                        <!-- Shop Card -->
                        <div class="bg-white bg-opacity-20 rounded-xl p-4 text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-shopping-cart text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-sm mb-1">Shop</h4>
                            <p class="text-xs text-blue-200">E-commerce</p>
                        </div>
                    </div>

                    <!-- Central Connection -->
                    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-link text-white text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
                <div class="absolute -bottom-4 -left-4 w-6 h-6 bg-green-400 rounded-full animate-pulse"></div>
                <div class="absolute top-1/2 -right-6 w-4 h-4 bg-purple-400 rounded-full animate-ping"></div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
    </div>
</section>
