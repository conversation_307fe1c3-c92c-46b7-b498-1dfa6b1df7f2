<?php
/**
 * JobSpace - Application Bootstrap
 * Professional Application Bootstrapper
 *
 * @package JobSpace\Bootstrap
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

// Load constants first
require_once __DIR__ . '/../config/constants.php';

// Load autoloader
require_once __DIR__ . '/autoload.php';

// Create and return application instance
return App::getInstance();