# JobSpace - Root .htaccess
# All requests go through public/index.php

RewriteEngine On

# Security - Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Protect configuration files
<FilesMatch "\.(env|ini|log|sh|sql|conf|config)$">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Prevent access to directories
Options -Indexes

# Prevent direct access to public directory in URL
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+public/ [NC]
RewriteRule ^public/(.*)$ /$1 [R=301,L]

# Send all requests to public/index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]