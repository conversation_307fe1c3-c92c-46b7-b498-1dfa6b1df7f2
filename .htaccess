# JobSpace - Root Directory Protection
# Redirect all requests to public directory

# Enable URL Rewriting
RewriteEngine On

# Redirect to public directory
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ /public/$1 [L,QSA]

# Security - Prevent access to sensitive files and directories
<FilesMatch "\.(env|git|htaccess|htpasswd|ini|log|sh|inc|bak|sql|md|json|lock|yml|yaml)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Prevent access to specific directories
RedirectMatch 404 /\.git
RedirectMatch 404 /app
RedirectMatch 404 /config
RedirectMatch 404 /database
RedirectMatch 404 /storage
RedirectMatch 404 /bootstrap
RedirectMatch 404 /routes
RedirectMatch 404 /tests
RedirectMatch 404 /vendor