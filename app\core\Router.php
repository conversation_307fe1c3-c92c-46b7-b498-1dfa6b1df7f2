<?php
/**
 * JobSpace - Router Core Class
 * Professional URL Routing System
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

class Router
{
    private static $routes = [];
    private static $groupStack = [];
    private static $currentGroup = [];
    private static $namedRoutes = [];

    /**
     * Add GET route
     */
    public static function get($uri, $action, $name = null)
    {
        return self::addRoute('GET', $uri, $action, $name);
    }

    /**
     * Add POST route
     */
    public static function post($uri, $action, $name = null)
    {
        return self::addRoute('POST', $uri, $action, $name);
    }

    /**
     * Add PUT route
     */
    public static function put($uri, $action, $name = null)
    {
        return self::addRoute('PUT', $uri, $action, $name);
    }

    /**
     * Add DELETE route
     */
    public static function delete($uri, $action, $name = null)
    {
        return self::addRoute('DELETE', $uri, $action, $name);
    }

    /**
     * Add PATCH route
     */
    public static function patch($uri, $action, $name = null)
    {
        return self::addRoute('PATCH', $uri, $action, $name);
    }

    /**
     * Add route for multiple methods
     */
    public static function match($methods, $uri, $action, $name = null)
    {
        foreach ((array) $methods as $method) {
            self::addRoute(strtoupper($method), $uri, $action, $name);
        }
    }

    /**
     * Add route for all methods
     */
    public static function any($uri, $action, $name = null)
    {
        return self::match(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], $uri, $action, $name);
    }

    /**
     * Create route group
     */
    public static function group($attributes, $callback)
    {
        self::$groupStack[] = $attributes;
        self::$currentGroup = self::mergeGroup(self::$currentGroup, $attributes);

        call_user_func($callback);

        array_pop(self::$groupStack);
        self::$currentGroup = end(self::$groupStack) ?: [];
    }

    /**
     * Add route
     */
    private static function addRoute($method, $uri, $action, $name = null)
    {
        // Apply group attributes
        $uri = self::applyGroupPrefix($uri);
        $action = self::applyGroupNamespace($action);
        $middleware = self::getGroupMiddleware();

        $route = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $middleware,
            'name' => $name,
            'compiled' => self::compileRoute($uri)
        ];

        self::$routes[] = $route;

        // Store named route
        if ($name) {
            self::$namedRoutes[$name] = $route;
        }

        return $route;
    }

    /**
     * Compile route pattern
     */
    private static function compileRoute($uri)
    {
        // Convert route parameters to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri);
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $pattern . '$/';
    }

    /**
     * Apply group prefix
     */
    private static function applyGroupPrefix($uri)
    {
        $prefix = self::$currentGroup['prefix'] ?? '';

        if ($prefix) {
            $uri = trim($prefix, '/') . '/' . ltrim($uri, '/');
        }

        return '/' . trim($uri, '/');
    }

    /**
     * Apply group namespace
     */
    private static function applyGroupNamespace($action)
    {
        $namespace = self::$currentGroup['namespace'] ?? '';

        if ($namespace && is_string($action)) {
            $action = $namespace . '\\' . $action;
        }

        return $action;
    }

    /**
     * Get group middleware
     */
    private static function getGroupMiddleware()
    {
        return self::$currentGroup['middleware'] ?? [];
    }

    /**
     * Merge group attributes
     */
    private static function mergeGroup($old, $new)
    {
        $merged = $old;

        // Merge prefix
        if (isset($new['prefix'])) {
            $oldPrefix = $old['prefix'] ?? '';
            $merged['prefix'] = trim($oldPrefix . '/' . $new['prefix'], '/');
        }

        // Merge namespace
        if (isset($new['namespace'])) {
            $oldNamespace = $old['namespace'] ?? '';
            $merged['namespace'] = trim($oldNamespace . '\\' . $new['namespace'], '\\');
        }

        // Merge middleware
        if (isset($new['middleware'])) {
            $oldMiddleware = $old['middleware'] ?? [];
            $newMiddleware = (array) $new['middleware'];
            $merged['middleware'] = array_merge($oldMiddleware, $newMiddleware);
        }

        return $merged;
    }

    /**
     * Dispatch request
     */
    public static function dispatch(Request $request)
    {
        $method = $request->getMethod();
        $uri = $request->getUri();

        // Find matching route
        foreach (self::$routes as $route) {
            if ($route['method'] === $method && preg_match($route['compiled'], $uri, $matches)) {
                // Extract parameters
                $params = self::extractParameters($route['uri'], $uri);
                $request->setParams($params);

                // Run middleware
                $middlewareResult = self::runMiddleware($route['middleware'], $request);
                if ($middlewareResult !== null) {
                    return $middlewareResult;
                }

                // Execute action
                return self::executeAction($route['action'], $request);
            }
        }

        // No route found
        return self::handleNotFound();
    }

    /**
     * Extract route parameters
     */
    private static function extractParameters($routeUri, $requestUri)
    {
        $routeParts = explode('/', trim($routeUri, '/'));
        $requestParts = explode('/', trim($requestUri, '/'));

        $params = [];

        foreach ($routeParts as $index => $part) {
            if (preg_match('/\{([^}]+)\}/', $part, $matches)) {
                $paramName = $matches[1];
                $params[$paramName] = $requestParts[$index] ?? null;
            }
        }

        return $params;
    }

    /**
     * Run middleware
     */
    private static function runMiddleware($middleware, Request $request)
    {
        foreach ((array) $middleware as $middlewareName) {
            $middlewareClass = self::resolveMiddleware($middlewareName);

            if ($middlewareClass) {
                $middlewareInstance = new $middlewareClass();
                $result = $middlewareInstance->handle($request);

                if ($result !== null) {
                    return $result;
                }
            }
        }

        return null;
    }

    /**
     * Resolve middleware class
     */
    private static function resolveMiddleware($name)
    {
        $middlewareMap = [
            'auth' => 'AuthMiddleware',
            'guest' => 'GuestMiddleware',
            'admin' => 'AdminMiddleware',
            'api' => 'ApiMiddleware',
            'throttle' => 'ThrottleMiddleware',
            'cors' => 'CorsMiddleware',
        ];

        $className = $middlewareMap[$name] ?? $name;

        // Check if class exists
        $fullClassName = $className;
        if (!class_exists($fullClassName)) {
            $fullClassName = 'App\\Middleware\\' . $className;
        }

        return class_exists($fullClassName) ? $fullClassName : null;
    }

    /**
     * Execute route action
     */
    private static function executeAction($action, Request $request)
    {
        if (is_callable($action)) {
            // Closure action
            return call_user_func($action, $request);
        }

        if (is_string($action)) {
            // Controller@method action
            if (strpos($action, '@') !== false) {
                list($controller, $method) = explode('@', $action);
                return self::callControllerMethod($controller, $method, $request);
            }

            // Single controller action
            return self::callControllerMethod($action, 'index', $request);
        }

        if (is_array($action) && count($action) === 2) {
            // [Controller::class, 'method'] action
            return self::callControllerMethod($action[0], $action[1], $request);
        }

        throw new Exception('Invalid route action');
    }

    /**
     * Call controller method
     */
    private static function callControllerMethod($controller, $method, Request $request)
    {
        // Resolve controller class
        $controllerClass = self::resolveController($controller);

        if (!class_exists($controllerClass)) {
            throw new Exception("Controller not found: {$controllerClass}");
        }

        $controllerInstance = new $controllerClass();

        if (!method_exists($controllerInstance, $method)) {
            throw new Exception("Method not found: {$controllerClass}@{$method}");
        }

        return $controllerInstance->$method($request);
    }

    /**
     * Resolve controller class
     */
    private static function resolveController($controller)
    {
        // If already a full class name
        if (class_exists($controller)) {
            return $controller;
        }

        // Try with App\Controllers namespace
        $fullClassName = 'App\\Controllers\\' . $controller;
        if (class_exists($fullClassName)) {
            return $fullClassName;
        }

        // Try without namespace (for backward compatibility)
        return $controller;
    }

    /**
     * Handle 404 Not Found
     */
    private static function handleNotFound()
    {
        http_response_code(404);

        // Try to render 404 view
        try {
            return View::render('errors/404', [
                'message' => 'Page not found'
            ]);
        } catch (Exception $e) {
            // Fallback 404 response
            return '<!DOCTYPE html>
            <html>
            <head>
                <title>404 - Page Not Found</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
                    .error { color: #721c24; }
                </style>
            </head>
            <body>
                <div class="error">
                    <h1>404 - Page Not Found</h1>
                    <p>The requested page could not be found.</p>
                    <a href="/">Go Home</a>
                </div>
            </body>
            </html>';
        }
    }

    /**
     * Generate URL for named route
     */
    public static function route($name, $params = [])
    {
        if (!isset(self::$namedRoutes[$name])) {
            throw new Exception("Named route not found: {$name}");
        }

        $route = self::$namedRoutes[$name];
        $uri = $route['uri'];

        // Replace parameters
        foreach ($params as $key => $value) {
            $uri = str_replace('{' . $key . '}', $value, $uri);
        }

        return app_url($uri);
    }

    /**
     * Get all routes
     */
    public static function getRoutes()
    {
        return self::$routes;
    }

    /**
     * Clear all routes
     */
    public static function clear()
    {
        self::$routes = [];
        self::$groupStack = [];
        self::$currentGroup = [];
        self::$namedRoutes = [];
    }
}