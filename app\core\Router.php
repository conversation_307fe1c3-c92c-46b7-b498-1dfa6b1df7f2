<?php
/**
 * JobSpace - URL Router
 * Professional Routing System
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class Router
{
    /**
     * Registered routes
     */
    private static $routes = [];

    /**
     * Current request
     */
    private static $request;

    /**
     * Register GET route
     */
    public static function get($uri, $action, $name = null)
    {
        self::addRoute('GET', $uri, $action, $name);
    }

    /**
     * Register POST route
     */
    public static function post($uri, $action, $name = null)
    {
        self::addRoute('POST', $uri, $action, $name);
    }

    /**
     * Register PUT route
     */
    public static function put($uri, $action, $name = null)
    {
        self::addRoute('PUT', $uri, $action, $name);
    }

    /**
     * Register DELETE route
     */
    public static function delete($uri, $action, $name = null)
    {
        self::addRoute('DELETE', $uri, $action, $name);
    }

    /**
     * Add route to registry
     */
    private static function addRoute($method, $uri, $action, $name = null)
    {
        // Clean URI
        $uri = '/' . trim($uri, '/');
        if ($uri !== '/') {
            $uri = rtrim($uri, '/');
        }

        // Compile route pattern
        $pattern = self::compileRoute($uri);

        self::$routes[] = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'name' => $name,
            'pattern' => $pattern
        ];
    }

    /**
     * Compile route pattern
     */
    private static function compileRoute($uri)
    {
        // Escape special regex characters
        $pattern = preg_quote($uri, '#');

        // Replace parameter placeholders
        $pattern = preg_replace('/\\\{([^}]+)\\\}/', '([^/]+)', $pattern);

        return '#^' . $pattern . '$#';
    }

    /**
     * Dispatch request
     */
    public static function dispatch(Request $request)
    {
        self::$request = $request;

        $method = $request->getMethod();
        $uri = $request->getUri();

        // Find matching route
        foreach (self::$routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $uri, $matches)) {
                // Extract parameters
                $params = self::extractParameters($route['uri'], $uri);
                $request->setParams($params);

                // Execute action
                return self::executeAction($route['action'], $request);
            }
        }

        // No route found - 404
        return self::handleNotFound();
    }

    /**
     * Extract route parameters
     */
    private static function extractParameters($routeUri, $requestUri)
    {
        $params = [];

        // Get parameter names from route
        preg_match_all('/\{([^}]+)\}/', $routeUri, $paramNames);

        // Get parameter values from request
        $routePattern = preg_replace('/\{[^}]+\}/', '([^/]+)', preg_quote($routeUri, '#'));
        preg_match('#^' . $routePattern . '$#', $requestUri, $paramValues);

        // Combine names and values
        if (!empty($paramNames[1]) && !empty($paramValues)) {
            array_shift($paramValues); // Remove full match
            $params = array_combine($paramNames[1], $paramValues);
        }

        return $params;
    }

    /**
     * Execute route action
     */
    private static function executeAction($action, Request $request)
    {
        if (is_string($action)) {
            // Controller@method format
            if (strpos($action, '@') !== false) {
                list($controllerName, $methodName) = explode('@', $action);
                return self::callController($controllerName, $methodName, $request);
            }

            // Function name
            if (function_exists($action)) {
                return call_user_func($action, $request);
            }
        }

        if (is_callable($action)) {
            return call_user_func($action, $request);
        }

        throw new Exception("Invalid route action: " . print_r($action, true));
    }

    /**
     * Call controller method
     */
    private static function callController($controllerName, $methodName, Request $request)
    {
        // Find controller file
        $controllerFile = self::findControllerFile($controllerName);

        if (!$controllerFile || !file_exists($controllerFile)) {
            throw new Exception("Controller file not found: {$controllerName}");
        }

        // Include controller file
        require_once $controllerFile;

        // Check if controller class exists
        if (!class_exists($controllerName)) {
            throw new Exception("Controller class not found: {$controllerName}");
        }

        // Create controller instance
        $controller = new $controllerName();

        // Check if method exists
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Controller method not found: {$controllerName}@{$methodName}");
        }

        // Call controller method
        return call_user_func([$controller, $methodName], $request);
    }

    /**
     * Find controller file
     */
    private static function findControllerFile($controllerName)
    {
        // Common controller locations
        $locations = [
            CONTROLLERS_PATH . '/' . $controllerName . '.php',
            CONTROLLERS_PATH . '/home/' . $controllerName . '.php',
            CONTROLLERS_PATH . '/auth/' . $controllerName . '.php',
            CONTROLLERS_PATH . '/admin/' . $controllerName . '.php',
            CONTROLLERS_PATH . '/api/' . $controllerName . '.php',
        ];

        foreach ($locations as $location) {
            if (file_exists($location)) {
                return $location;
            }
        }

        return null;
    }

    /**
     * Handle 404 Not Found
     */
    private static function handleNotFound()
    {
        $response = new Response();
        return $response->notFound('The requested page was not found.');
    }

    /**
     * Generate URL for named route
     */
    public static function url($name, $params = [])
    {
        foreach (self::$routes as $route) {
            if ($route['name'] === $name) {
                $url = $route['uri'];

                // Replace parameters
                foreach ($params as $key => $value) {
                    $url = str_replace('{' . $key . '}', $value, $url);
                }

                return BASE_URL . $url;
            }
        }

        throw new Exception("Named route not found: {$name}");
    }

    /**
     * Get all registered routes
     */
    public static function getRoutes()
    {
        return self::$routes;
    }

    /**
     * Clear all routes
     */
    public static function clearRoutes()
    {
        self::$routes = [];
    }
}