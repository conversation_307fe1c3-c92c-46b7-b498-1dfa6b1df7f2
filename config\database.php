<?php
/**
 * JobSpace - Database Configuration
 * Professional Database Settings
 *
 * @package JobSpace
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

return [

    // =============================================================================
    // DEFAULT DATABASE CONNECTION
    // =============================================================================

    'default' => 'mysql',

    // =============================================================================
    // DATABASE CONNECTIONS
    // =============================================================================

    'connections' => [

        'mysql' => [
            'driver' => 'mysql',
            'host' => 'localhost',
            'port' => '3306',
            'database' => 'jobspace',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => 'InnoDB',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'"
            ],
        ],

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => DATABASE_PATH . '/jobspace.sqlite',
            'prefix' => '',
            'foreign_key_constraints' => true,
        ],

        'testing' => [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
            'foreign_key_constraints' => true,
        ],

    ],

    // =============================================================================
    // MIGRATION SETTINGS
    // =============================================================================

    'migrations' => [
        'table' => 'migrations',
        'path' => DATABASE_PATH . '/migrations',
    ],

    // =============================================================================
    // REDIS CONFIGURATION
    // =============================================================================

    'redis' => [

        'client' => 'phpredis',

        'options' => [
            'cluster' => 'redis',
            'prefix' => 'jobspace_database_',
        ],

        'default' => [
            'url' => null,
            'host' => '127.0.0.1',
            'password' => null,
            'port' => 6379,
            'database' => 0,
        ],

        'cache' => [
            'url' => null,
            'host' => '127.0.0.1',
            'password' => null,
            'port' => 6379,
            'database' => 1,
        ],

    ],

    // =============================================================================
    // BACKUP SETTINGS
    // =============================================================================

    'backup' => [
        'enabled' => true,
        'path' => BACKUPS_PATH,
        'frequency' => 'daily', // daily, weekly, monthly
        'keep_backups' => 30, // number of backups to keep
        'compress' => true,
        'tables' => [], // empty array means all tables
        'exclude_tables' => [
            'sessions',
            'cache',
            'logs',
        ],
    ],

    // =============================================================================
    // QUERY LOGGING
    // =============================================================================

    'query_log' => [
        'enabled' => DEBUG_MODE,
        'slow_query_threshold' => 1000, // milliseconds
        'log_file' => LOGS_PATH . '/queries.log',
    ],

    // =============================================================================
    // CONNECTION POOLING
    // =============================================================================

    'pool' => [
        'enabled' => false,
        'min_connections' => 1,
        'max_connections' => 10,
        'idle_timeout' => 60, // seconds
    ],

];