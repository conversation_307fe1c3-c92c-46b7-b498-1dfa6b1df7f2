<?php
/**
 * JobSpace - URL Helper Functions
 * Professional URL Management
 * 
 * @package JobSpace\Helpers
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Generate application URL
 */
function url($path = '', $params = [])
{
    $url = BASE_URL;
    
    if ($path) {
        $url .= '/' . ltrim($path, '/');
    }
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * Generate asset URL
 */
function asset($path = '')
{
    return ASSETS_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Generate upload URL
 */
function upload($path = '')
{
    return UPLOADS_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Generate route URL by name
 */
function route($name, $params = [])
{
    try {
        return Router::route($name, $params);
    } catch (Exception $e) {
        return url();
    }
}

/**
 * Redirect to URL
 */
function redirect($url, $statusCode = 302)
{
    if (!headers_sent()) {
        header("Location: $url", true, $statusCode);
        exit;
    }
}

/**
 * Redirect back to previous page
 */
function back()
{
    $referer = $_SERVER['HTTP_REFERER'] ?? url();
    redirect($referer);
}

/**
 * Get current URL
 */
function current_url()
{
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    
    return $protocol . $host . $uri;
}

/**
 * Check if current URL matches pattern
 */
function is_current_url($pattern)
{
    $currentPath = parse_url(current_url(), PHP_URL_PATH);
    return fnmatch($pattern, $currentPath);
}

/**
 * Get query parameter
 */
function get_query($key = null, $default = null)
{
    if ($key === null) {
        return $_GET;
    }
    
    return $_GET[$key] ?? $default;
}

/**
 * Build query string
 */
function build_query($params)
{
    return http_build_query($params);
}

/**
 * Add query parameters to URL
 */
function add_query($url, $params)
{
    $parsedUrl = parse_url($url);
    $query = [];
    
    if (isset($parsedUrl['query'])) {
        parse_str($parsedUrl['query'], $query);
    }
    
    $query = array_merge($query, $params);
    
    $newUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    
    if (isset($parsedUrl['port'])) {
        $newUrl .= ':' . $parsedUrl['port'];
    }
    
    if (isset($parsedUrl['path'])) {
        $newUrl .= $parsedUrl['path'];
    }
    
    if (!empty($query)) {
        $newUrl .= '?' . http_build_query($query);
    }
    
    if (isset($parsedUrl['fragment'])) {
        $newUrl .= '#' . $parsedUrl['fragment'];
    }
    
    return $newUrl;
}

/**
 * Remove query parameters from URL
 */
function remove_query($url, $keys)
{
    $parsedUrl = parse_url($url);
    $query = [];
    
    if (isset($parsedUrl['query'])) {
        parse_str($parsedUrl['query'], $query);
    }
    
    foreach ((array) $keys as $key) {
        unset($query[$key]);
    }
    
    $newUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    
    if (isset($parsedUrl['port'])) {
        $newUrl .= ':' . $parsedUrl['port'];
    }
    
    if (isset($parsedUrl['path'])) {
        $newUrl .= $parsedUrl['path'];
    }
    
    if (!empty($query)) {
        $newUrl .= '?' . http_build_query($query);
    }
    
    if (isset($parsedUrl['fragment'])) {
        $newUrl .= '#' . $parsedUrl['fragment'];
    }
    
    return $newUrl;
}

/**
 * Generate pagination URL
 */
function pagination_url($page, $params = [])
{
    $params['page'] = $page;
    return add_query(current_url(), $params);
}

/**
 * Generate secure URL (HTTPS)
 */
function secure_url($path = '', $params = [])
{
    $url = str_replace('http://', 'https://', url($path, $params));
    return $url;
}

/**
 * Check if URL is secure
 */
function is_secure()
{
    return !empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off';
}

/**
 * Get domain from URL
 */
function get_domain($url = null)
{
    if ($url === null) {
        $url = current_url();
    }
    
    return parse_url($url, PHP_URL_HOST);
}

/**
 * Check if URL is external
 */
function is_external_url($url)
{
    $currentDomain = get_domain();
    $urlDomain = get_domain($url);
    
    return $urlDomain && $urlDomain !== $currentDomain;
}

/**
 * Sanitize URL
 */
function sanitize_url($url)
{
    return filter_var($url, FILTER_SANITIZE_URL);
}

/**
 * Validate URL
 */
function is_valid_url($url)
{
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Shorten URL (basic implementation)
 */
function shorten_url($url, $length = 50)
{
    if (strlen($url) <= $length) {
        return $url;
    }
    
    return substr($url, 0, $length - 3) . '...';
}

/**
 * Get URL slug from string
 */
function make_slug($string)
{
    $slug = strtolower($string);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug;
}

/**
 * Generate API URL
 */
function api_url($endpoint = '', $version = 'v1')
{
    return url("api/{$version}/" . ltrim($endpoint, '/'));
}

/**
 * Generate admin URL
 */
function admin_url($path = '')
{
    return url('admin/' . ltrim($path, '/'));
}

/**
 * Generate dashboard URL
 */
function dashboard_url($path = '')
{
    return url('dashboard/' . ltrim($path, '/'));
}

/**
 * Generate feed URL
 */
function feed_url($path = '')
{
    return url('feed/' . ltrim($path, '/'));
}
