<?php
/**
 * JobSpace - Application Configuration
 * Professional App Settings
 *
 * @package JobSpace
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

return [

    // =============================================================================
    // APPLICATION SETTINGS
    // =============================================================================

    'name' => APP_NAME,
    'version' => APP_VERSION,
    'description' => APP_DESCRIPTION,
    'keywords' => APP_KEYWORDS,
    'author' => APP_AUTHOR,
    'environment' => APP_ENV,

    // =============================================================================
    // URL & ROUTING
    // =============================================================================

    'url' => BASE_URL,
    'asset_url' => ASSETS_URL,
    'upload_url' => UPLOADS_URL,

    'routing' => [
        'default_controller' => 'HomeController',
        'default_method' => 'index',
        'url_suffix' => '',
        'enable_query_strings' => false,
    ],

    // =============================================================================
    // SECURITY
    // =============================================================================

    'security' => [
        'key' => SECURITY_KEY,
        'csrf_protection' => true,
        'csrf_token_name' => CSRF_TOKEN_NAME,
        'csrf_expire' => 7200,
        'encryption_key' => 'jobspace_encryption_key_2024',
        'hash_algorithm' => 'sha256',
    ],

    // =============================================================================
    // SESSION CONFIGURATION
    // =============================================================================

    'session' => [
        'driver' => 'file', // file, database, redis
        'name' => SESSION_NAME,
        'lifetime' => 120, // minutes
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => STORAGE_PATH . '/sessions',
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => [
            'name' => SESSION_NAME,
            'path' => '/',
            'domain' => null,
            'secure' => false,
            'http_only' => true,
            'same_site' => 'lax',
        ],
    ],

    // =============================================================================
    // CACHE CONFIGURATION
    // =============================================================================

    'cache' => [
        'default' => 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => CACHE_PATH,
            ],
            'redis' => [
                'driver' => 'redis',
                'connection' => 'cache',
            ],
        ],
        'prefix' => 'jobspace_cache',
        'lifetime' => CACHE_LIFETIME,
    ],

    // =============================================================================
    // VIEW CONFIGURATION
    // =============================================================================

    'view' => [
        'paths' => [
            VIEWS_PATH,
            TEMPLATES_PATH,
            COMPONENTS_PATH,
        ],
        'compiled' => CACHE_PATH . '/views',
        'cache' => VIEW_CACHE_ENABLED,
        'default_layout' => 'app',
        'default_section' => 'content',
    ],

    // =============================================================================
    // LOGGING
    // =============================================================================

    'logging' => [
        'default' => 'single',
        'channels' => [
            'single' => [
                'driver' => 'single',
                'path' => LOGS_PATH . '/jobspace.log',
                'level' => 'debug',
            ],
            'daily' => [
                'driver' => 'daily',
                'path' => LOGS_PATH . '/jobspace.log',
                'level' => 'debug',
                'days' => 14,
            ],
        ],
    ],

    // =============================================================================
    // FILE UPLOADS
    // =============================================================================

    'uploads' => [
        'path' => UPLOADS_PATH,
        'url' => UPLOADS_URL,
        'max_size' => MAX_FILE_SIZE,
        'allowed_types' => [
            'images' => ALLOWED_IMAGE_TYPES,
            'documents' => ALLOWED_DOCUMENT_TYPES,
        ],
        'image_quality' => 85,
        'create_thumbnails' => true,
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600],
        ],
    ],

    // =============================================================================
    // PAGINATION
    // =============================================================================

    'pagination' => [
        'default_per_page' => DEFAULT_PAGE_SIZE,
        'max_per_page' => MAX_PAGE_SIZE,
        'page_name' => 'page',
        'view' => 'pagination.default',
    ],

    // =============================================================================
    // LOCALIZATION
    // =============================================================================

    'locale' => [
        'default' => DEFAULT_LOCALE,
        'fallback' => 'en',
        'available' => ['en', 'bn'],
        'path' => RESOURCES_PATH . '/lang',
    ],

    // =============================================================================
    // TIMEZONE
    // =============================================================================

    'timezone' => DEFAULT_TIMEZONE,

    // =============================================================================
    // ERROR HANDLING
    // =============================================================================

    'debug' => DEBUG_MODE,
    'log_errors' => LOG_ERRORS,
    'display_errors' => DISPLAY_ERRORS,

    // =============================================================================
    // MODULES & FEATURES
    // =============================================================================

    'modules' => MODULES,

    'features' => [
        'quiz_system' => true,
        'freelance_marketplace' => true,
        'social_media' => true,
        'ecommerce' => true,
        'payment_gateway' => true,
        'analytics' => true,
        'notifications' => true,
        'messaging' => true,
        'file_sharing' => true,
        'multi_language' => true,
        'pwa_support' => true,
        'api_access' => true,
    ],

    // =============================================================================
    // API CONFIGURATION
    // =============================================================================

    'api' => [
        'version' => API_VERSION,
        'rate_limit' => API_RATE_LIMIT,
        'token_expiry' => API_TOKEN_EXPIRY,
        'prefix' => 'api',
        'middleware' => ['api', 'throttle'],
    ],

    // =============================================================================
    // MAIL CONFIGURATION
    // =============================================================================

    'mail' => [
        'default' => 'smtp',
        'mailers' => [
            'smtp' => [
                'transport' => 'smtp',
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'encryption' => 'tls',
                'username' => '',
                'password' => '',
                'timeout' => null,
            ],
        ],
        'from' => [
            'address' => '<EMAIL>',
            'name' => APP_NAME,
        ],
    ],

];