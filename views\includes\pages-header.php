<!-- JobSpace - Public Header Component -->
<header class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-4">
        <!-- Top Bar -->
        <div class="hidden md:flex justify-between items-center py-2 text-sm border-b border-gray-100">
            <div class="flex items-center space-x-4 text-gray-600">
                <span><i class="fas fa-envelope mr-1"></i> <EMAIL></span>
                <span><i class="fas fa-phone mr-1"></i> +880 1234-567890</span>
            </div>
            <div class="flex items-center space-x-4">
                <?php if (isset($isAuthenticated) && $isAuthenticated): ?>
                    <a href="/dashboard" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                    </a>
                    <form method="POST" action="/logout" class="inline">
                        <?= View::csrf() ?>
                        <button type="submit" class="text-red-600 hover:text-red-800 transition-colors">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </form>
                <?php else: ?>
                    <a href="/login" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-sign-in-alt mr-1"></i> Login
                    </a>
                    <a href="/register" class="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors">
                        <i class="fas fa-user-plus mr-1"></i> Register
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="flex items-center justify-between py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="/" class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">J</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800"><?= $appName ?? 'JobSpace' ?></h1>
                        <p class="text-xs text-gray-500 -mt-1">Learn • Earn • Connect • Trade</p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-8">
                <a href="/" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                    <i class="fas fa-home mr-1"></i> Home
                </a>
                
                <div class="relative group">
                    <button class="text-gray-700 hover:text-blue-600 transition-colors font-medium flex items-center">
                        <i class="fas fa-brain mr-1"></i> Quiz
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </button>
                    <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                        <a href="/quiz" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-t-lg">
                            <i class="fas fa-list mr-2"></i> All Quizzes
                        </a>
                        <a href="/quiz/category/programming" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                            <i class="fas fa-code mr-2"></i> Programming
                        </a>
                        <a href="/quiz/category/general" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-b-lg">
                            <i class="fas fa-globe mr-2"></i> General Knowledge
                        </a>
                    </div>
                </div>

                <div class="relative group">
                    <button class="text-gray-700 hover:text-blue-600 transition-colors font-medium flex items-center">
                        <i class="fas fa-briefcase mr-1"></i> Jobs
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </button>
                    <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                        <a href="/freelance" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-t-lg">
                            <i class="fas fa-search mr-2"></i> Find Jobs
                        </a>
                        <a href="/freelance/post-job" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-b-lg">
                            <i class="fas fa-plus mr-2"></i> Post Job
                        </a>
                    </div>
                </div>

                <a href="/shop" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                    <i class="fas fa-shopping-cart mr-1"></i> Shop
                </a>

                <a href="/about" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                    <i class="fas fa-info-circle mr-1"></i> About
                </a>

                <a href="/contact" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                    <i class="fas fa-envelope mr-1"></i> Contact
                </a>
            </div>

            <!-- Search Bar -->
            <div class="hidden md:flex items-center">
                <form action="/search" method="GET" class="relative">
                    <input type="text" name="q" placeholder="Search..." 
                           class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </form>
            </div>

            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-blue-600 transition-colors">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </nav>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="lg:hidden bg-white border-t border-gray-200 hidden">
        <div class="container mx-auto px-4 py-4">
            <!-- Mobile Search -->
            <form action="/search" method="GET" class="mb-4">
                <div class="relative">
                    <input type="text" name="q" placeholder="Search..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </form>

            <!-- Mobile Menu Items -->
            <div class="space-y-2">
                <a href="/" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-home mr-2"></i> Home
                </a>
                <a href="/quiz" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-brain mr-2"></i> Quiz
                </a>
                <a href="/freelance" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-briefcase mr-2"></i> Jobs
                </a>
                <a href="/shop" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-shopping-cart mr-2"></i> Shop
                </a>
                <a href="/about" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-info-circle mr-2"></i> About
                </a>
                <a href="/contact" class="block py-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <i class="fas fa-envelope mr-2"></i> Contact
                </a>
                
                <!-- Mobile Auth Links -->
                <div class="border-t border-gray-200 pt-4 mt-4">
                    <?php if (isset($isAuthenticated) && $isAuthenticated): ?>
                        <a href="/dashboard" class="block py-2 text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                        <form method="POST" action="/logout" class="mt-2">
                            <?= View::csrf() ?>
                            <button type="submit" class="w-full text-left py-2 text-red-600 hover:text-red-800 transition-colors">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </button>
                        </form>
                    <?php else: ?>
                        <a href="/login" class="block py-2 text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i> Login
                        </a>
                        <a href="/register" class="block py-2 text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fas fa-user-plus mr-2"></i> Register
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Mobile Menu Toggle Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Toggle icon
            const icon = mobileMenuBtn.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars text-xl';
            } else {
                icon.className = 'fas fa-times text-xl';
            }
        });
    }
});
</script>
