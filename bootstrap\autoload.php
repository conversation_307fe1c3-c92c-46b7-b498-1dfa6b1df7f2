<?php
/**
 * JobSpace - Autoloader
 * Professional Class Autoloader
 *
 * @package JobSpace\Bootstrap
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * JobSpace Autoloader Class
 */
class JobSpaceAutoloader
{
    /**
     * Class map for faster loading
     */
    private static $classMap = [];

    /**
     * Namespace to directory mapping
     */
    private static $namespacePaths = [];

    /**
     * Register autoloader
     */
    public static function register()
    {
        spl_autoload_register([__CLASS__, 'autoload']);
        self::initializeClassMap();
        self::initializeNamespaces();
    }

    /**
     * Initialize class map
     */
    private static function initializeClassMap()
    {
        self::$classMap = [
            // Core classes
            'Database' => CORE_PATH . '/Database.php',
            'Request' => CORE_PATH . '/Request.php',
            'Response' => CORE_PATH . '/Response.php',
            'Router' => CORE_PATH . '/Router.php',
            'Controller' => CORE_PATH . '/Controller.php',
            'App' => CORE_PATH . '/App.php',

            // Controllers
            'HomeController' => CONTROLLERS_PATH . '/home/<USER>',
            'AuthController' => CONTROLLERS_PATH . '/auth/AuthController.php',
            'AdminController' => CONTROLLERS_PATH . '/admin/AdminController.php',
            'UserController' => CONTROLLERS_PATH . '/user/UserController.php',

            // Models (when created)
            'User' => MODELS_PATH . '/User.php',
            'Quiz' => MODELS_PATH . '/Quiz.php',
            'Job' => MODELS_PATH . '/Job.php',
            'Product' => MODELS_PATH . '/Product.php',
        ];
    }

    /**
     * Initialize namespace paths
     */
    private static function initializeNamespaces()
    {
        self::$namespacePaths = [
            'JobSpace\\Core\\' => CORE_PATH,
            'JobSpace\\Controllers\\' => CONTROLLERS_PATH,
            'JobSpace\\Models\\' => MODELS_PATH,
            'JobSpace\\Helpers\\' => HELPERS_PATH,
            'JobSpace\\Middleware\\' => MIDDLEWARE_PATH,
        ];
    }

    /**
     * Autoload classes
     */
    public static function autoload($className)
    {
        // Try class map first (fastest)
        if (isset(self::$classMap[$className])) {
            $file = self::$classMap[$className];
            if (file_exists($file)) {
                require_once $file;
                return true;
            }
        }

        // Try namespace mapping
        foreach (self::$namespacePaths as $namespace => $path) {
            if (strpos($className, $namespace) === 0) {
                $relativeClass = substr($className, strlen($namespace));
                $file = $path . '/' . str_replace('\\', '/', $relativeClass) . '.php';

                if (file_exists($file)) {
                    require_once $file;
                    return true;
                }
            }
        }

        // Try PSR-4 style loading
        $file = self::findClassFile($className);
        if ($file && file_exists($file)) {
            require_once $file;
            return true;
        }

        return false;
    }

    /**
     * Find class file using various strategies
     */
    private static function findClassFile($className)
    {
        // Remove namespace prefix if any
        $className = ltrim($className, '\\');

        // Convert namespace separators to directory separators
        $classPath = str_replace('\\', '/', $className) . '.php';

        // Search in common directories
        $searchPaths = [
            CORE_PATH,
            CONTROLLERS_PATH,
            MODELS_PATH,
            HELPERS_PATH,
            MIDDLEWARE_PATH,
        ];

        foreach ($searchPaths as $basePath) {
            $fullPath = $basePath . '/' . $classPath;
            if (file_exists($fullPath)) {
                return $fullPath;
            }

            // Try with just the class name
            $simplePath = $basePath . '/' . basename($classPath);
            if (file_exists($simplePath)) {
                return $simplePath;
            }
        }

        // Try recursive search in controllers directory
        $controllerFile = self::findControllerRecursive($className);
        if ($controllerFile) {
            return $controllerFile;
        }

        return null;
    }

    /**
     * Find controller file recursively
     */
    private static function findControllerRecursive($className)
    {
        if (!str_ends_with($className, 'Controller')) {
            return null;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(CONTROLLERS_PATH, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $filename = $file->getBasename('.php');
                if ($filename === $className) {
                    return $file->getPathname();
                }
            }
        }

        return null;
    }

    /**
     * Add class to map
     */
    public static function addClassMap($className, $filePath)
    {
        self::$classMap[$className] = $filePath;
    }

    /**
     * Add namespace path
     */
    public static function addNamespacePath($namespace, $path)
    {
        self::$namespacePaths[$namespace] = $path;
    }

    /**
     * Get class map
     */
    public static function getClassMap()
    {
        return self::$classMap;
    }

    /**
     * Get namespace paths
     */
    public static function getNamespacePaths()
    {
        return self::$namespacePaths;
    }
}

// Register the autoloader
JobSpaceAutoloader::register();

// Load helper functions if they exist
$helperFiles = [
    HELPERS_PATH . '/functions.php',
    HELPERS_PATH . '/url_helpers.php',
    HELPERS_PATH . '/string_helpers.php',
    HELPERS_PATH . '/array_helpers.php',
];

foreach ($helperFiles as $helperFile) {
    if (file_exists($helperFile)) {
        require_once $helperFile;
    }
}