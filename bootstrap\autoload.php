<?php
/**
 * JobSpace - Autoloader
 * Professional Class Autoloading System
 *
 * @package JobSpace\Bootstrap
 * @version 1.0.0
 */

// Define access constant
define('JOBSPACE_ACCESS', true);

// Load constants first
require_once __DIR__ . '/../config/constants.php';

/**
 * JobSpace Autoloader Class
 */
class JobSpaceAutoloader
{
    private static $classMap = [];
    private static $namespaces = [];

    /**
     * Register autoloader
     */
    public static function register()
    {
        spl_autoload_register([__CLASS__, 'loadClass']);
        self::registerNamespaces();
        self::loadCoreClasses();
    }

    /**
     * Load class
     */
    public static function loadClass($className)
    {
        // Check class map first
        if (isset(self::$classMap[$className])) {
            require_once self::$classMap[$className];
            return true;
        }

        // Try namespace-based loading
        foreach (self::$namespaces as $namespace => $path) {
            if (strpos($className, $namespace) === 0) {
                $relativePath = substr($className, strlen($namespace));
                $filePath = $path . str_replace('\\', '/', $relativePath) . '.php';

                if (file_exists($filePath)) {
                    require_once $filePath;
                    return true;
                }
            }
        }

        // Try direct file loading
        $possiblePaths = [
            // Core classes
            CORE_PATH . '/' . $className . '.php',

            // Controllers
            CONTROLLERS_PATH . '/' . $className . '.php',
            CONTROLLERS_PATH . '/home/' . $className . '.php',
            CONTROLLERS_PATH . '/frontend/' . $className . '.php',
            CONTROLLERS_PATH . '/admin/' . $className . '.php',
            CONTROLLERS_PATH . '/auth/' . $className . '.php',
            CONTROLLERS_PATH . '/api/' . $className . '.php',

            // Models
            MODELS_PATH . '/' . $className . '.php',

            // Helpers
            HELPERS_PATH . '/' . $className . '.php',

            // Middleware
            MIDDLEWARE_PATH . '/' . $className . '.php',

            // Services
            SERVICES_PATH . '/' . $className . '.php',
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                require_once $path;
                return true;
            }
        }

        return false;
    }

    /**
     * Register namespaces
     */
    private static function registerNamespaces()
    {
        self::$namespaces = [
            'App\\Controllers\\' => CONTROLLERS_PATH . '/',
            'App\\Models\\' => MODELS_PATH . '/',
            'App\\Core\\' => CORE_PATH . '/',
            'App\\Helpers\\' => HELPERS_PATH . '/',
            'App\\Middleware\\' => MIDDLEWARE_PATH . '/',
            'App\\Services\\' => SERVICES_PATH . '/',
        ];
    }

    /**
     * Load core classes immediately
     */
    private static function loadCoreClasses()
    {
        $coreClasses = [
            'Database' => CORE_PATH . '/Database.php',
            'Request' => CORE_PATH . '/Request.php',
            'Response' => CORE_PATH . '/Response.php',
            'View' => CORE_PATH . '/View.php',
            'Controller' => CORE_PATH . '/Controller.php',
            'Router' => CORE_PATH . '/Router.php',
            'App' => CORE_PATH . '/App.php',
        ];

        foreach ($coreClasses as $className => $filePath) {
            if (file_exists($filePath)) {
                require_once $filePath;
                self::$classMap[$className] = $filePath;
            }
        }
    }

    /**
     * Add class to map
     */
    public static function addClassMap($className, $filePath)
    {
        self::$classMap[$className] = $filePath;
    }

    /**
     * Add namespace
     */
    public static function addNamespace($namespace, $path)
    {
        self::$namespaces[$namespace] = $path;
    }

    /**
     * Get loaded classes
     */
    public static function getLoadedClasses()
    {
        return array_keys(self::$classMap);
    }
}

/**
 * Helper Functions
 */

/**
 * Load helper file
 */
function load_helper($helper)
{
    $helperFile = HELPERS_PATH . '/' . $helper . '.php';
    if (file_exists($helperFile)) {
        require_once $helperFile;
        return true;
    }
    return false;
}

/**
 * Load configuration file
 */
function load_config($config)
{
    $configFile = CONFIG_PATH . '/' . $config . '.php';
    if (file_exists($configFile)) {
        return require $configFile;
    }
    return null;
}

/**
 * Load language file
 */
function load_language($language, $locale = null)
{
    $locale = $locale ?: DEFAULT_LOCALE;
    $langFile = RESOURCES_PATH . '/lang/' . $locale . '/' . $language . '.php';

    if (file_exists($langFile)) {
        return require $langFile;
    }

    // Fallback to English
    $fallbackFile = RESOURCES_PATH . '/lang/en/' . $language . '.php';
    if (file_exists($fallbackFile)) {
        return require $fallbackFile;
    }

    return [];
}

/**
 * Create directory if not exists
 */
function ensure_directory($path)
{
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}

/**
 * Initialize required directories
 */
function init_directories()
{
    $directories = [
        CACHE_PATH,
        LOGS_PATH,
        UPLOADS_PATH,
        STORAGE_PATH . '/sessions',
    ];

    foreach ($directories as $dir) {
        ensure_directory($dir);
    }
}

// Register autoloader
JobSpaceAutoloader::register();

// Initialize directories
init_directories();

// Load common helpers
$commonHelpers = [
    'url',
    'string',
    'array',
    'validation',
];

foreach ($commonHelpers as $helper) {
    load_helper($helper);
}