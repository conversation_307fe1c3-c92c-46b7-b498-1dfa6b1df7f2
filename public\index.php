<?php
/**
 * JobSpace - Main Entry Point
 * Professional Application Entry Point
 *
 * @package JobSpace
 * @version 1.0.0
 * <AUTHOR> Team
 */

/**
 * ============================================================================
 * JOBSPACE - COMPLETE MULTI-PLATFORM ECOSYSTEM
 * Learn • Earn • Connect • Trade
 * ============================================================================
 *
 * A comprehensive platform featuring:
 * - Quiz & Education System
 * - Freelance Marketplace
 * - Social Media Platform
 * - E-commerce System
 * - Payment Integration
 * - Analytics Dashboard
 * - Multi-language Support
 * - Progressive Web App (PWA)
 *
 * Built with professional PHP MVC architecture
 * Designed for scalability and performance
 * ============================================================================
 */

// Performance monitoring start
$startTime = microtime(true);
$startMemory = memory_get_usage();

// Bootstrap the application
$app = require_once __DIR__ . '/../bootstrap/app.php';

// Run the application
$app->run();

// Performance monitoring end (only in debug mode)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    $endTime = microtime(true);
    $endMemory = memory_get_usage();

    $executionTime = round(($endTime - $startTime) * 1000, 2);
    $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2);
    $peakMemory = round(memory_get_peak_usage() / 1024 / 1024, 2);

    echo "<!-- JobSpace Performance Stats -->";
    echo "<!-- Execution Time: {$executionTime}ms -->";
    echo "<!-- Memory Usage: {$memoryUsage}MB -->";
    echo "<!-- Peak Memory: {$peakMemory}MB -->";
    echo "<!-- Generated: " . date('Y-m-d H:i:s') . " -->";
}