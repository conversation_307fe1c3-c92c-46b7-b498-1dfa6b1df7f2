<?php
/**
 * JobSpace - Main Entry Point
 * Professional Application Entry Point
 *
 * @package JobSpace
 * @version 1.0.0
 */

// Define access constant
define('JOBSPACE_ACCESS', true);

// Start output buffering
ob_start();

// Start session
session_start();

try {
    // Bootstrap the application
    $app = require_once __DIR__ . '/../bootstrap/app.php';

    // Handle the request
    $app->run();

} catch (Exception $e) {
    // Handle errors gracefully
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        echo "<h1>Something went wrong</h1>";
        echo "<p>Please try again later.</p>";
    }
}

// End output buffering
ob_end_flush();