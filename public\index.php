<?php
/**
 * JobSpace - Application Entry Point
 * Professional Front Controller
 *
 * @package JobSpace
 * @version 1.0.0
 */

// Define access constant
define('JOBSPACE_ACCESS', true);

// Start output buffering
ob_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Bootstrap the application
    $app = require_once __DIR__ . '/../bootstrap/app.php';

    // Run the application
    $app->run();

} catch (Exception $e) {
    // Handle any uncaught exceptions
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<h3>Application Error:</h3>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    } else {
        // Production error page
        http_response_code(500);
        echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Server Error - JobSpace</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #e74c3c; margin-bottom: 20px; }
        p { color: #666; margin-bottom: 30px; }
        a { color: #3498db; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class='error-container'>
        <h1>500 - Server Error</h1>
        <p>Sorry, something went wrong on our end. Please try again later.</p>
        <a href='/'>← Back to Home</a>
    </div>
</body>
</html>";
    }

    // Log the error
    error_log("JobSpace Application Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}

// End output buffering and send
ob_end_flush();