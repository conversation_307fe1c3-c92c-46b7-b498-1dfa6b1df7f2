<?php
/**
 * JobSpace - Main Entry Point
 * Professional Application Entry Point
 *
 * @package JobSpace
 * @version 1.0.0
 * <AUTHOR> Team
 */

/**
 * ============================================================================
 * JOBSPACE - COMPLETE MULTI-PLATFORM ECOSYSTEM
 * Learn • Earn • Connect • Trade
 * ============================================================================
 *
 * A comprehensive platform featuring:
 * - Quiz & Education System
 * - Freelance Marketplace
 * - Social Media Platform
 * - E-commerce System
 * - Payment Integration
 * - Analytics Dashboard
 * - Multi-language Support
 * - Progressive Web App (PWA)
 *
 * Built with professional PHP MVC architecture
 * Designed for scalability and performance
 * ============================================================================
 */

// Define access constant
define('JOBSPACE_ACCESS', true);

// Performance monitoring start
$startTime = microtime(true);
$startMemory = memory_get_usage();

// Debug information (only in development)
if (isset($_GET['debug'])) {
    echo "<h3>Debug Information:</h3>";
    echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
    echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
    echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
    echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
    echo "<p><strong>Bootstrap Path:</strong> " . __DIR__ . '/../bootstrap/app.php' . "</p>";
    echo "<p><strong>Bootstrap Exists:</strong> " . (file_exists(__DIR__ . '/../bootstrap/app.php') ? 'Yes' : 'No') . "</p>";
    echo "<hr>";
}

try {
    // Bootstrap the application
    if (isset($_GET['debug'])) {
        echo "<p>Loading bootstrap...</p>";
    }

    $app = require_once __DIR__ . '/../bootstrap/app.php';

    if (isset($_GET['debug'])) {
        echo "<p>Bootstrap loaded, running app...</p>";
    }

    // Run the application
    $app->run();

} catch (Exception $e) {
    // Error handling
    if (isset($_GET['debug']) || (defined('APP_ENV') && APP_ENV === 'development')) {
        echo "<h3>Error:</h3>";
        echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        echo "An error occurred. Please try again later.";
    }
} catch (Error $e) {
    // Handle fatal errors
    if (isset($_GET['debug']) || (defined('APP_ENV') && APP_ENV === 'development')) {
        echo "<h3>Fatal Error:</h3>";
        echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        echo "A fatal error occurred. Please try again later.";
    }
}

// Performance monitoring end (only in debug mode)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    $endTime = microtime(true);
    $endMemory = memory_get_usage();

    $executionTime = round(($endTime - $startTime) * 1000, 2);
    $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2);
    $peakMemory = round(memory_get_peak_usage() / 1024 / 1024, 2);

    echo "<!-- JobSpace Performance Stats -->";
    echo "<!-- Execution Time: {$executionTime}ms -->";
    echo "<!-- Memory Usage: {$memoryUsage}MB -->";
    echo "<!-- Peak Memory: {$peakMemory}MB -->";
    echo "<!-- Generated: " . date('Y-m-d H:i:s') . " -->";
}