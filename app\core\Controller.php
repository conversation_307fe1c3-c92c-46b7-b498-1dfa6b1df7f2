<?php
/**
 * JobSpace - Base Controller
 * Professional Controller Base Class
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

class Controller
{
    /**
     * Request instance
     */
    protected $request;

    /**
     * Response instance
     */
    protected $response;

    /**
     * Database instance
     */
    protected $db;

    /**
     * View data
     */
    protected $data = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();

        try {
            $this->db = Database::getInstance();
        } catch (Exception $e) {
            // Database not available - continue without it
            $this->db = null;
        }

        // Initialize controller
        $this->init();
    }

    /**
     * Initialize controller (override in child classes)
     */
    protected function init()
    {
        // Override in child classes
    }

    /**
     * Set view data
     */
    protected function setData($key, $value = null)
    {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
    }

    /**
     * Get view data
     */
    protected function getData($key = null)
    {
        if ($key === null) {
            return $this->data;
        }

        return $this->data[$key] ?? null;
    }

    /**
     * Render view
     */
    protected function view($viewPath, $data = [])
    {
        // Merge data
        $viewData = array_merge($this->data, $data);

        // Extract data to variables
        extract($viewData);

        // Build view file path
        $viewFile = VIEWS_PATH . '/' . ltrim($viewPath, '/') . '.php';

        // Check if view file exists
        if (!file_exists($viewFile)) {
            throw new Exception("View file not found: {$viewFile}");
        }

        // Start output buffering
        ob_start();

        // Include view file
        include $viewFile;

        // Get content
        $content = ob_get_clean();

        // Send response
        $this->response->html($content);

        return $this->response;
    }

    /**
     * Render JSON response
     */
    protected function json($data, $statusCode = 200)
    {
        return $this->response->json($data, $statusCode);
    }

    /**
     * Redirect to URL
     */
    protected function redirect($url, $statusCode = 302)
    {
        return $this->response->redirect($url, $statusCode);
    }

    /**
     * Redirect back
     */
    protected function back()
    {
        return $this->response->back();
    }

    /**
     * Get request input
     */
    protected function input($key = null, $default = null)
    {
        return $this->request->input($key, $default);
    }

    /**
     * Get route parameter
     */
    protected function param($key, $default = null)
    {
        return $this->request->getParam($key, $default);
    }

    /**
     * Check if request is POST
     */
    protected function isPost()
    {
        return $this->request->isPost();
    }

    /**
     * Check if request is AJAX
     */
    protected function isAjax()
    {
        return $this->request->isAjax();
    }

    /**
     * Validate CSRF token
     */
    protected function validateCsrf()
    {
        $token = $this->input('_token');
        $sessionToken = $_SESSION['_token'] ?? '';

        if (!$token || !$sessionToken || !hash_equals($sessionToken, $token)) {
            throw new Exception('CSRF token mismatch');
        }

        return true;
    }

    /**
     * Generate CSRF token
     */
    protected function generateCsrf()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $token = bin2hex(random_bytes(32));
        $_SESSION['_token'] = $token;

        return $token;
    }

    /**
     * Set flash message
     */
    protected function flash($type, $message)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['flash'][] = [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get flash messages
     */
    protected function getFlash()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);

        return $messages;
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * Get authenticated user
     */
    protected function getUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return $_SESSION['user'] ?? null;
    }

    /**
     * Require authentication
     */
    protected function requireAuth()
    {
        if (!$this->isAuthenticated()) {
            $this->redirect(BASE_URL . '/login');
            exit;
        }
    }

    /**
     * Require specific role
     */
    protected function requireRole($role)
    {
        $this->requireAuth();

        $user = $this->getUser();
        if (!$user || $user['role'] !== $role) {
            $this->response->forbidden('Access denied');
            exit;
        }
    }
}