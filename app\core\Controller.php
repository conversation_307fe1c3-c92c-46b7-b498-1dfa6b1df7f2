<?php
/**
 * JobSpace - Base Controller Class
 * Professional Controller Foundation
 *
 * @package JobSpace\Core
 * @version 1.0.0
 */

abstract class Controller
{
    protected $request;
    protected $response;
    protected $db;
    protected $view;
    protected $data = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
        $this->db = Database::getInstance();
        $this->view = new View();

        // Initialize controller
        $this->init();
    }

    /**
     * Initialize controller (override in child classes)
     */
    protected function init()
    {
        // Override in child classes for custom initialization
    }

    /**
     * Set view data
     */
    protected function setData($key, $value = null)
    {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }

        return $this;
    }

    /**
     * Get view data
     */
    protected function getData($key = null)
    {
        if ($key === null) {
            return $this->data;
        }

        return $this->data[$key] ?? null;
    }

    /**
     * Render view
     */
    protected function view($view, $data = [], $layout = null)
    {
        $data = array_merge($this->data, $data);

        // Add common data
        $data = array_merge($data, $this->getCommonData());

        return View::render($view, $data, $layout);
    }

    /**
     * Render JSON response
     */
    protected function json($data, $statusCode = 200)
    {
        return $this->response->json($data, $statusCode);
    }

    /**
     * Redirect to URL
     */
    protected function redirect($url, $statusCode = 302)
    {
        return $this->response->redirect($url, $statusCode);
    }

    /**
     * Redirect back
     */
    protected function back()
    {
        return $this->response->back();
    }

    /**
     * Return success response
     */
    protected function success($data = null, $message = 'Success')
    {
        if ($this->request->isAjax()) {
            return $this->json([
                'success' => true,
                'message' => $message,
                'data' => $data
            ]);
        }

        // Set flash message for non-AJAX requests
        $this->setFlash('success', $message);
        return $this->back();
    }

    /**
     * Return error response
     */
    protected function error($message = 'Error occurred', $errors = null, $statusCode = 400)
    {
        if ($this->request->isAjax()) {
            return $this->json([
                'success' => false,
                'message' => $message,
                'errors' => $errors
            ], $statusCode);
        }

        // Set flash message for non-AJAX requests
        $this->setFlash('error', $message);
        return $this->back();
    }

    /**
     * Return validation error response
     */
    protected function validationError($errors, $message = 'Validation failed')
    {
        return $this->error($message, $errors, 422);
    }

    /**
     * Validate request data
     */
    protected function validate($rules, $messages = [])
    {
        $errors = $this->request->validate($rules);

        if (!empty($errors)) {
            throw new ValidationException($errors, $messages);
        }

        return true;
    }

    /**
     * Get input value
     */
    protected function input($key = null, $default = null)
    {
        return $this->request->input($key, $default);
    }

    /**
     * Get query parameter
     */
    protected function query($key = null, $default = null)
    {
        return $this->request->query($key, $default);
    }

    /**
     * Get POST data
     */
    protected function post($key = null, $default = null)
    {
        return $this->request->post($key, $default);
    }

    /**
     * Get route parameter
     */
    protected function param($key, $default = null)
    {
        return $this->request->param($key, $default);
    }

    /**
     * Get uploaded file
     */
    protected function file($key)
    {
        return $this->request->file($key);
    }

    /**
     * Check if file was uploaded
     */
    protected function hasFile($key)
    {
        return $this->request->hasFile($key);
    }

    /**
     * Set flash message
     */
    protected function setFlash($type, $message)
    {
        if (!isset($_SESSION)) {
            session_start();
        }

        $_SESSION['flash'][$type] = $message;
    }

    /**
     * Get flash message
     */
    protected function getFlash($type = null)
    {
        if (!isset($_SESSION)) {
            session_start();
        }

        if ($type === null) {
            $flash = $_SESSION['flash'] ?? [];
            unset($_SESSION['flash']);
            return $flash;
        }

        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);

        return $message;
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated()
    {
        return isset($_SESSION['user_id']);
    }

    /**
     * Get current user
     */
    protected function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        $userId = $_SESSION['user_id'];
        return $this->db->fetch("SELECT * FROM users WHERE id = :id", ['id' => $userId]);
    }

    /**
     * Require authentication
     */
    protected function requireAuth()
    {
        if (!$this->isAuthenticated()) {
            if ($this->request->isAjax()) {
                return $this->json(['message' => 'Authentication required'], 401);
            }

            return $this->redirect('/login');
        }
    }

    /**
     * Check user role
     */
    protected function hasRole($role)
    {
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }

    /**
     * Require specific role
     */
    protected function requireRole($role)
    {
        $this->requireAuth();

        if (!$this->hasRole($role)) {
            if ($this->request->isAjax()) {
                return $this->json(['message' => 'Insufficient permissions'], 403);
            }

            return $this->redirect('/unauthorized');
        }
    }

    /**
     * Get common data for all views
     */
    protected function getCommonData()
    {
        $data = [
            'currentUser' => $this->getCurrentUser(),
            'isAuthenticated' => $this->isAuthenticated(),
            'flash' => $this->getFlash(),
            'request' => $this->request,
            'baseUrl' => BASE_URL,
            'assetUrl' => ASSETS_URL,
            'appName' => APP_NAME,
            'appVersion' => APP_VERSION,
        ];

        // Add page meta data
        $data['pageTitle'] = $this->getData('pageTitle') ?: APP_NAME;
        $data['pageDescription'] = $this->getData('pageDescription') ?: APP_DESCRIPTION;
        $data['pageKeywords'] = $this->getData('pageKeywords') ?: APP_KEYWORDS;

        return $data;
    }

    /**
     * Handle file upload
     */
    protected function uploadFile($fileKey, $destination = 'uploads', $allowedTypes = null)
    {
        if (!$this->hasFile($fileKey)) {
            return false;
        }

        $file = $this->file($fileKey);
        $allowedTypes = $allowedTypes ?: ALLOWED_IMAGE_TYPES;

        // Validate file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('Invalid file type');
        }

        // Validate file size
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size too large');
        }

        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $uploadPath = UPLOADS_PATH . '/' . $destination;

        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        $filePath = $uploadPath . '/' . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $destination . '/' . $filename;
        }

        return false;
    }

    /**
     * Log activity
     */
    protected function logActivity($action, $details = null)
    {
        $user = $this->getCurrentUser();

        $logData = [
            'user_id' => $user['id'] ?? null,
            'action' => $action,
            'details' => $details ? json_encode($details) : null,
            'ip_address' => $this->request->getClientIp(),
            'user_agent' => $this->request->getUserAgent(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert('activity_logs', $logData);
    }
}

/**
 * Validation Exception Class
 */
class ValidationException extends Exception
{
    private $errors;
    private $messages;

    public function __construct($errors, $messages = [])
    {
        $this->errors = $errors;
        $this->messages = $messages;

        $message = 'Validation failed';
        if (!empty($errors)) {
            $firstError = reset($errors);
            $message = is_array($firstError) ? $firstError[0] : $firstError;
        }

        parent::__construct($message);
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function getMessages()
    {
        return $this->messages;
    }
}