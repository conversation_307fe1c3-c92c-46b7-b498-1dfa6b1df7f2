<?php
/**
 * JobSpace - Web Routes
 * Professional Web Routing Configuration
 *
 * @package JobSpace\Routes
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * ============================================================================
 * PUBLIC ROUTES
 * ============================================================================
 */

// Home Page
Router::get('/', 'HomeController@index', 'home');

// Static Pages
Router::get('/about', 'PageController@about', 'about');
Router::get('/contact', 'PageController@contact', 'contact');
Router::get('/privacy', 'PageController@privacy', 'privacy');
Router::get('/terms', 'PageController@terms', 'terms');
Router::get('/faq', 'PageController@faq', 'faq');
Router::get('/help', 'PageController@help', 'help');

// Contact Form
Router::post('/contact', 'PageController@submitContact', 'contact.submit');

/**
 * ============================================================================
 * AUTHENTICATION ROUTES
 * ============================================================================
 */

// Guest routes (only for non-authenticated users)
Router::group(['middleware' => 'guest'], function() {
    // Login
    Router::get('/login', 'AuthController@showLogin', 'login');
    Router::post('/login', 'AuthController@login', 'login.submit');

    // Register
    Router::get('/register', 'AuthController@showRegister', 'register');
    Router::post('/register', 'AuthController@register', 'register.submit');

    // Password Reset
    Router::get('/forgot-password', 'AuthController@showForgotPassword', 'password.forgot');
    Router::post('/forgot-password', 'AuthController@forgotPassword', 'password.forgot.submit');
    Router::get('/reset-password/{token}', 'AuthController@showResetPassword', 'password.reset');
    Router::post('/reset-password', 'AuthController@resetPassword', 'password.reset.submit');
});

// Logout (for authenticated users)
Router::post('/logout', 'AuthController@logout', 'logout')->middleware('auth');

/**
 * ============================================================================
 * USER DASHBOARD ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'dashboard', 'middleware' => 'auth'], function() {
    // User Dashboard
    Router::get('/', 'DashboardController@index', 'dashboard');
    Router::get('/profile', 'DashboardController@profile', 'dashboard.profile');
    Router::post('/profile', 'DashboardController@updateProfile', 'dashboard.profile.update');

    // Settings
    Router::get('/settings', 'DashboardController@settings', 'dashboard.settings');
    Router::post('/settings', 'DashboardController@updateSettings', 'dashboard.settings.update');
});

/**
 * ============================================================================
 * FEED ROUTES (Main User Interface)
 * ============================================================================
 */

Router::group(['prefix' => 'feed', 'middleware' => 'auth'], function() {
    // Main Feed
    Router::get('/', 'FeedController@index', 'feed');

    // Quiz Feed
    Router::get('/quiz', 'FeedController@quiz', 'feed.quiz');

    // Social Feed
    Router::get('/social', 'FeedController@social', 'feed.social');

    // E-commerce Feed
    Router::get('/shop', 'FeedController@shop', 'feed.shop');

    // Freelance Feed
    Router::get('/jobs', 'FeedController@jobs', 'feed.jobs');
});

/**
 * ============================================================================
 * QUIZ SYSTEM ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'quiz'], function() {
    // Public quiz listing
    Router::get('/', 'QuizController@index', 'quiz.index');
    Router::get('/category/{category}', 'QuizController@category', 'quiz.category');
    Router::get('/{id}', 'QuizController@show', 'quiz.show');

    // Authenticated quiz routes
    Router::group(['middleware' => 'auth'], function() {
        Router::get('/{id}/start', 'QuizController@start', 'quiz.start');
        Router::post('/{id}/submit', 'QuizController@submit', 'quiz.submit');
        Router::get('/{id}/result', 'QuizController@result', 'quiz.result');
        Router::get('/my-results', 'QuizController@myResults', 'quiz.my-results');
    });
});

/**
 * ============================================================================
 * FREELANCE MARKETPLACE ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'freelance'], function() {
    // Public job listings
    Router::get('/', 'FreelanceController@index', 'freelance.index');
    Router::get('/jobs', 'FreelanceController@jobs', 'freelance.jobs');
    Router::get('/job/{id}', 'FreelanceController@showJob', 'freelance.job.show');

    // Authenticated freelance routes
    Router::group(['middleware' => 'auth'], function() {
        // Job applications
        Router::post('/job/{id}/apply', 'FreelanceController@applyJob', 'freelance.job.apply');

        // My jobs/applications
        Router::get('/my-jobs', 'FreelanceController@myJobs', 'freelance.my-jobs');
        Router::get('/my-applications', 'FreelanceController@myApplications', 'freelance.my-applications');

        // Post job (for employers)
        Router::get('/post-job', 'FreelanceController@showPostJob', 'freelance.post-job');
        Router::post('/post-job', 'FreelanceController@postJob', 'freelance.post-job.submit');
    });
});

/**
 * ============================================================================
 * SOCIAL MEDIA ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'social', 'middleware' => 'auth'], function() {
    // Social feed
    Router::get('/', 'SocialController@index', 'social.index');

    // Posts
    Router::post('/post', 'SocialController@createPost', 'social.post.create');
    Router::get('/post/{id}', 'SocialController@showPost', 'social.post.show');
    Router::post('/post/{id}/like', 'SocialController@likePost', 'social.post.like');
    Router::post('/post/{id}/comment', 'SocialController@commentPost', 'social.post.comment');

    // User profiles
    Router::get('/profile/{username}', 'SocialController@showProfile', 'social.profile');
    Router::post('/follow/{userId}', 'SocialController@follow', 'social.follow');
    Router::post('/unfollow/{userId}', 'SocialController@unfollow', 'social.unfollow');
});

/**
 * ============================================================================
 * E-COMMERCE ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'shop'], function() {
    // Public shop
    Router::get('/', 'ShopController@index', 'shop.index');
    Router::get('/category/{category}', 'ShopController@category', 'shop.category');
    Router::get('/product/{id}', 'ShopController@showProduct', 'shop.product.show');

    // Authenticated shop routes
    Router::group(['middleware' => 'auth'], function() {
        // Cart
        Router::get('/cart', 'ShopController@cart', 'shop.cart');
        Router::post('/cart/add', 'ShopController@addToCart', 'shop.cart.add');
        Router::post('/cart/remove', 'ShopController@removeFromCart', 'shop.cart.remove');
        Router::post('/cart/update', 'ShopController@updateCart', 'shop.cart.update');

        // Checkout
        Router::get('/checkout', 'ShopController@checkout', 'shop.checkout');
        Router::post('/checkout', 'ShopController@processCheckout', 'shop.checkout.process');

        // Orders
        Router::get('/orders', 'ShopController@orders', 'shop.orders');
        Router::get('/order/{id}', 'ShopController@showOrder', 'shop.order.show');
    });
});

/**
 * ============================================================================
 * SEARCH ROUTES
 * ============================================================================
 */

Router::get('/search', 'SearchController@index', 'search');
Router::get('/search/quiz', 'SearchController@quiz', 'search.quiz');
Router::get('/search/jobs', 'SearchController@jobs', 'search.jobs');
Router::get('/search/products', 'SearchController@products', 'search.products');
Router::get('/search/users', 'SearchController@users', 'search.users');

/**
 * ============================================================================
 * NOTIFICATION ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'notifications', 'middleware' => 'auth'], function() {
    Router::get('/', 'NotificationController@index', 'notifications.index');
    Router::post('/mark-read/{id}', 'NotificationController@markRead', 'notifications.mark-read');
    Router::post('/mark-all-read', 'NotificationController@markAllRead', 'notifications.mark-all-read');
});

/**
 * ============================================================================
 * FILE UPLOAD ROUTES
 * ============================================================================
 */

Router::group(['prefix' => 'upload', 'middleware' => 'auth'], function() {
    Router::post('/image', 'UploadController@image', 'upload.image');
    Router::post('/file', 'UploadController@file', 'upload.file');
    Router::post('/avatar', 'UploadController@avatar', 'upload.avatar');
});

/**
 * ============================================================================
 * ERROR ROUTES
 * ============================================================================
 */

Router::get('/unauthorized', function() {
    return View::render('errors/403', ['message' => 'Access forbidden']);
}, 'unauthorized');

Router::get('/404', function() {
    return View::render('errors/404', ['message' => 'Page not found']);
}, '404');