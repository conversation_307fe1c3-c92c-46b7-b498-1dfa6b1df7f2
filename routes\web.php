<?php
/**
 * JobSpace - Web Routes
 * Professional Route Definitions
 *
 * @package JobSpace\Routes
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('JOBSPACE_ACCESS')) {
    die('Direct access not permitted');
}

// =============================================================================
// PUBLIC ROUTES
// =============================================================================

// Home page
Router::get('/', 'HomeController@index', 'home');

// About page
Router::get('/about', 'HomeController@about', 'about');

// Contact page
Router::get('/contact', 'HomeController@contact', 'contact');

// =============================================================================
// AUTHENTICATION ROUTES
// =============================================================================

// Login
Router::get('/login', 'AuthController@showLogin', 'login');
Router::post('/login', 'AuthController@login', 'login.post');

// Register
Router::get('/register', 'AuthController@showRegister', 'register');
Router::post('/register', 'AuthController@register', 'register.post');

// Logout
Router::post('/logout', 'AuthController@logout', 'logout');

// Password Reset
Router::get('/forgot-password', 'AuthController@showForgotPassword', 'password.forgot');
Router::post('/forgot-password', 'AuthController@forgotPassword', 'password.forgot.post');
Router::get('/reset-password/{token}', 'AuthController@showResetPassword', 'password.reset');
Router::post('/reset-password', 'AuthController@resetPassword', 'password.reset.post');

// =============================================================================
// QUIZ ROUTES
// =============================================================================

// Quiz listing
Router::get('/quiz', 'QuizController@index', 'quiz.index');

// Quiz details
Router::get('/quiz/{id}', 'QuizController@show', 'quiz.show');

// Start quiz
Router::get('/quiz/{id}/start', 'QuizController@start', 'quiz.start');

// Submit quiz
Router::post('/quiz/{id}/submit', 'QuizController@submit', 'quiz.submit');

// Quiz results
Router::get('/quiz/{id}/results', 'QuizController@results', 'quiz.results');

// =============================================================================
// FREELANCE ROUTES
// =============================================================================

// Job listing
Router::get('/freelance', 'FreelanceController@index', 'freelance.index');
Router::get('/freelance/jobs', 'FreelanceController@jobs', 'freelance.jobs');

// Job details
Router::get('/freelance/job/{id}', 'FreelanceController@showJob', 'freelance.job.show');

// Post job
Router::get('/freelance/post-job', 'FreelanceController@showPostJob', 'freelance.job.create');
Router::post('/freelance/post-job', 'FreelanceController@postJob', 'freelance.job.store');

// Apply for job
Router::post('/freelance/job/{id}/apply', 'FreelanceController@applyJob', 'freelance.job.apply');

// =============================================================================
// SOCIAL MEDIA ROUTES
// =============================================================================

// Social feed
Router::get('/social', 'SocialController@index', 'social.index');
Router::get('/social/feed', 'SocialController@feed', 'social.feed');

// Create post
Router::post('/social/post', 'SocialController@createPost', 'social.post.create');

// Like/Unlike post
Router::post('/social/post/{id}/like', 'SocialController@likePost', 'social.post.like');

// Comment on post
Router::post('/social/post/{id}/comment', 'SocialController@commentPost', 'social.post.comment');

// User profile
Router::get('/profile/{username}', 'SocialController@profile', 'social.profile');

// =============================================================================
// E-COMMERCE ROUTES
// =============================================================================

// Product listing
Router::get('/ecommerce', 'EcommerceController@index', 'ecommerce.index');
Router::get('/shop', 'EcommerceController@index', 'shop.index');

// Product details
Router::get('/ecommerce/product/{id}', 'EcommerceController@showProduct', 'ecommerce.product.show');

// Add to cart
Router::post('/ecommerce/cart/add', 'EcommerceController@addToCart', 'ecommerce.cart.add');

// Cart
Router::get('/ecommerce/cart', 'EcommerceController@cart', 'ecommerce.cart');

// Checkout
Router::get('/ecommerce/checkout', 'EcommerceController@checkout', 'ecommerce.checkout');
Router::post('/ecommerce/checkout', 'EcommerceController@processCheckout', 'ecommerce.checkout.process');

// =============================================================================
// USER DASHBOARD ROUTES
// =============================================================================

// User dashboard
Router::get('/dashboard', 'UserController@dashboard', 'user.dashboard');

// User profile
Router::get('/dashboard/profile', 'UserController@profile', 'user.profile');
Router::post('/dashboard/profile', 'UserController@updateProfile', 'user.profile.update');

// User settings
Router::get('/dashboard/settings', 'UserController@settings', 'user.settings');
Router::post('/dashboard/settings', 'UserController@updateSettings', 'user.settings.update');

// =============================================================================
// ADMIN ROUTES
// =============================================================================

// Admin dashboard
Router::get('/admin', 'AdminController@dashboard', 'admin.dashboard');
Router::get('/admin/dashboard', 'AdminController@dashboard', 'admin.dashboard.main');

// User management
Router::get('/admin/users', 'AdminController@users', 'admin.users');
Router::get('/admin/users/{id}', 'AdminController@showUser', 'admin.users.show');
Router::post('/admin/users/{id}/update', 'AdminController@updateUser', 'admin.users.update');

// Quiz management
Router::get('/admin/quizzes', 'AdminController@quizzes', 'admin.quizzes');
Router::get('/admin/quizzes/create', 'AdminController@createQuiz', 'admin.quizzes.create');
Router::post('/admin/quizzes', 'AdminController@storeQuiz', 'admin.quizzes.store');

// Job management
Router::get('/admin/jobs', 'AdminController@jobs', 'admin.jobs');

// Product management
Router::get('/admin/products', 'AdminController@products', 'admin.products');

// Settings
Router::get('/admin/settings', 'AdminController@settings', 'admin.settings');
Router::post('/admin/settings', 'AdminController@updateSettings', 'admin.settings.update');

// =============================================================================
// API ROUTES
// =============================================================================

// API endpoints for AJAX requests
Router::get('/api/stats', 'ApiController@stats', 'api.stats');
Router::get('/api/notifications', 'ApiController@notifications', 'api.notifications');
Router::post('/api/upload', 'ApiController@upload', 'api.upload');

// =============================================================================
// LEGAL PAGES
// =============================================================================

// Privacy policy
Router::get('/privacy', 'HomeController@privacy', 'privacy');

// Terms of service
Router::get('/terms', 'HomeController@terms', 'terms');

// Help center
Router::get('/help', 'HomeController@help', 'help');

// FAQ
Router::get('/faq', 'HomeController@faq', 'faq');