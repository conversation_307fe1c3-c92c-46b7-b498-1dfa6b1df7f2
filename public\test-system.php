<?php
/**
 * JobSpace - System Test
 * Complete system testing and verification
 */

// Define access constant
define('JOBSPACE_ACCESS', true);

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>JobSpace System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; background: #f8f9fa; }
        .test-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
        .links { margin: 20px 0; }
        .links a { display: inline-block; margin: 5px 10px 5px 0; padding: 10px 15px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
        .links a:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 JobSpace System Test</h1>
        <p><strong>Testing Date:</strong> " . date('Y-m-d H:i:s') . "</p>
        <hr>";

// Test 1: Constants Loading
echo "<div class='section'>
        <h2>1. Constants & Configuration</h2>";

try {
    require_once __DIR__ . '/../config/constants.php';
    echo "<div class='test-item success'>✅ Constants loaded successfully</div>";
    echo "<div class='test-item'>📁 ROOT_PATH: " . ROOT_PATH . "</div>";
    echo "<div class='test-item'>🌐 BASE_URL: " . BASE_URL . "</div>";
    echo "<div class='test-item'>📂 VIEWS_PATH: " . VIEWS_PATH . "</div>";
    echo "<div class='test-item'>🗄️ APP_NAME: " . APP_NAME . "</div>";
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Constants Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 2: Autoloader
echo "<div class='section'>
        <h2>2. Autoloader & Core Classes</h2>";

try {
    require_once __DIR__ . '/../bootstrap/autoload.php';
    echo "<div class='test-item success'>✅ Autoloader loaded successfully</div>";
    
    // Test core classes
    $classes = ['Database', 'Request', 'Response', 'Router', 'Controller', 'App'];
    foreach ($classes as $class) {
        if (class_exists($class)) {
            echo "<div class='test-item success'>✅ Class {$class} available</div>";
        } else {
            echo "<div class='test-item error'>❌ Class {$class} not found</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Autoloader Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Request Processing
echo "<div class='section'>
        <h2>3. Request Processing</h2>";

try {
    $request = new Request();
    echo "<div class='test-item success'>✅ Request object created</div>";
    echo "<div class='test-item'>📝 Method: " . $request->getMethod() . "</div>";
    echo "<div class='test-item'>🔗 URI: " . $request->getUri() . "</div>";
    echo "<div class='test-item'>🌐 Base URL: " . $request->getBaseUrl() . "</div>";
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Request Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 4: Router
echo "<div class='section'>
        <h2>4. Router System</h2>";

try {
    // Load routes
    require_once __DIR__ . '/../routes/web.php';
    echo "<div class='test-item success'>✅ Routes loaded successfully</div>";
    
    // Test route registration
    Router::get('/test-route', 'HomeController@index', 'test');
    echo "<div class='test-item success'>✅ Test route registered</div>";
    
    // Get all routes
    $routes = Router::getRoutes();
    echo "<div class='test-item'>📊 Total routes registered: " . count($routes) . "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Router Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 5: Controller
echo "<div class='section'>
        <h2>5. Controller System</h2>";

try {
    if (file_exists(__DIR__ . '/../app/controllers/home/<USER>')) {
        require_once __DIR__ . '/../app/controllers/home/<USER>';
        echo "<div class='test-item success'>✅ HomeController file found</div>";
        
        if (class_exists('HomeController')) {
            echo "<div class='test-item success'>✅ HomeController class available</div>";
            
            // Test controller instantiation
            $controller = new HomeController();
            echo "<div class='test-item success'>✅ HomeController instantiated successfully</div>";
        } else {
            echo "<div class='test-item error'>❌ HomeController class not found</div>";
        }
    } else {
        echo "<div class='test-item error'>❌ HomeController file not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Controller Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 6: View System
echo "<div class='section'>
        <h2>6. View System</h2>";

$viewFiles = [
    'pages/home.php' => 'Home page',
    'includes/public-header.php' => 'Public header',
    'includes/public-footer.php' => 'Public footer'
];

foreach ($viewFiles as $file => $description) {
    $fullPath = VIEWS_PATH . '/' . $file;
    if (file_exists($fullPath)) {
        echo "<div class='test-item success'>✅ {$description} exists</div>";
    } else {
        echo "<div class='test-item error'>❌ {$description} not found</div>";
    }
}

echo "</div>";

// Test 7: Application Bootstrap
echo "<div class='section'>
        <h2>7. Application Bootstrap</h2>";

try {
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    echo "<div class='test-item success'>✅ Application bootstrapped successfully</div>";
    echo "<div class='test-item'>🏗️ App Class: " . get_class($app) . "</div>";
    echo "<div class='test-item'>⚡ App Booted: " . ($app->isBooted() ? 'Yes' : 'No') . "</div>";
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Bootstrap Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 8: File Permissions
echo "<div class='section'>
        <h2>8. File Permissions</h2>";

$checkPaths = [
    ROOT_PATH . '/storage' => 'Storage directory',
    ROOT_PATH . '/storage/logs' => 'Logs directory',
    ROOT_PATH . '/storage/cache' => 'Cache directory',
    ROOT_PATH . '/public' => 'Public directory'
];

foreach ($checkPaths as $path => $description) {
    if (file_exists($path)) {
        if (is_writable($path)) {
            echo "<div class='test-item success'>✅ {$description} is writable</div>";
        } else {
            echo "<div class='test-item warning'>⚠️ {$description} is not writable</div>";
        }
    } else {
        echo "<div class='test-item warning'>⚠️ {$description} does not exist</div>";
    }
}

echo "</div>";

// Test 9: PHP Configuration
echo "<div class='section'>
        <h2>9. PHP Configuration</h2>";

echo "<div class='test-item'>🐘 PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='test-item'>💾 Memory Limit: " . ini_get('memory_limit') . "</div>";
echo "<div class='test-item'>⏱️ Max Execution Time: " . ini_get('max_execution_time') . "s</div>";
echo "<div class='test-item'>📁 Upload Max Filesize: " . ini_get('upload_max_filesize') . "</div>";
echo "<div class='test-item'>📊 Post Max Size: " . ini_get('post_max_size') . "</div>";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='test-item success'>✅ Extension {$ext} loaded</div>";
    } else {
        echo "<div class='test-item error'>❌ Extension {$ext} not loaded</div>";
    }
}

echo "</div>";

// Quick Links
echo "<div class='section'>
        <h2>🔗 Quick Links</h2>
        <div class='links'>
            <a href='" . BASE_URL . "/' target='_blank'>🏠 Home Page</a>
            <a href='" . BASE_URL . "/about' target='_blank'>ℹ️ About Page</a>
            <a href='" . BASE_URL . "/contact' target='_blank'>📞 Contact Page</a>
            <a href='" . BASE_URL . "/login' target='_blank'>🔐 Login Page</a>
            <a href='" . BASE_URL . "/register' target='_blank'>📝 Register Page</a>
            <a href='" . BASE_URL . "/quiz' target='_blank'>🧠 Quiz Section</a>
            <a href='" . BASE_URL . "/freelance' target='_blank'>💼 Freelance Section</a>
            <a href='" . BASE_URL . "/social' target='_blank'>👥 Social Section</a>
            <a href='" . BASE_URL . "/ecommerce' target='_blank'>🛒 E-commerce Section</a>
        </div>
      </div>";

echo "<div class='section'>
        <h2>✅ System Status</h2>
        <div class='test-item success'>
            <strong>🎉 JobSpace system is ready!</strong><br>
            All core components are loaded and functional. You can now access the home page and start using the platform.
        </div>
        <div class='test-item'>
            <strong>Next Steps:</strong><br>
            1. Visit the home page to see the complete design<br>
            2. Test navigation between different sections<br>
            3. Check responsive design on mobile devices<br>
            4. Set up database for full functionality
        </div>
      </div>";

echo "</div>
</body>
</html>";
?>
