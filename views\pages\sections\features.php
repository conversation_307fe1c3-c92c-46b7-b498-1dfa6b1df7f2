<!-- JobSpace - Features Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                Powerful Features for
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                    Everyone
                </span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover a comprehensive ecosystem that combines learning, earning, networking, and trading
                in one powerful platform designed for your success.
            </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php if (isset($features) && is_array($features)): ?>
                <?php foreach ($features as $index => $feature): ?>
                    <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-r from-<?= $feature['color'] ?>-100 to-<?= $feature['color'] ?>-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="<?= $feature['icon'] ?> text-<?= $feature['color'] ?>-600 text-2xl"></i>
                        </div>

                        <!-- Content -->
                        <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-<?= $feature['color'] ?>-600 transition-colors duration-300">
                            <?= htmlspecialchars($feature['title']) ?>
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            <?= htmlspecialchars($feature['description']) ?>
                        </p>

                        <!-- Hover Effect -->
                        <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="w-12 h-1 bg-gradient-to-r from-<?= $feature['color'] ?>-400 to-<?= $feature['color'] ?>-600 rounded-full"></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Fallback Features -->
                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-brain text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                        Interactive Learning
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Test your knowledge with engaging quizzes and earn rewards while learning new skills.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"></div>
                    </div>
                </div>

                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-100 to-green-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-briefcase text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-green-600 transition-colors duration-300">
                        Freelance Marketplace
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Find freelance opportunities or hire talented professionals for your projects.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-green-400 to-green-600 rounded-full"></div>
                    </div>
                </div>

                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                        Social Networking
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Connect with like-minded people and build your professional network.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full"></div>
                    </div>
                </div>

                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-shopping-cart text-orange-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-orange-600 transition-colors duration-300">
                        E-commerce Platform
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Buy and sell products in our integrated marketplace with secure payments.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"></div>
                    </div>
                </div>

                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-indigo-100 to-indigo-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-wallet text-indigo-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-indigo-600 transition-colors duration-300">
                        Digital Wallet
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Manage your earnings and payments with our secure digital wallet system.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-full"></div>
                    </div>
                </div>

                <div class="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                    <div class="w-16 h-16 bg-gradient-to-r from-red-100 to-red-200 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-red-600 transition-colors duration-300">
                        Analytics Dashboard
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Track your progress and performance with detailed analytics and insights.
                    </p>
                    <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="w-12 h-1 bg-gradient-to-r from-red-400 to-red-600 rounded-full"></div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12">
                <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">
                    Ready to Experience All Features?
                </h3>
                <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
                    Join thousands of users who are already benefiting from our comprehensive platform.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <?php if (isset($isAuthenticated) && $isAuthenticated): ?>
                        <a href="/dashboard"
                           class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Explore Dashboard
                        </a>
                    <?php else: ?>
                        <a href="/register"
                           class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-rocket mr-2"></i>
                            Start Free Today
                        </a>
                        <a href="/about"
                           class="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>